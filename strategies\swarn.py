"""
Swarm Trading Strategy - Integrated with TradingBot Architecture

A sophisticated swarm-based trading system where individual agents start with $0.30 capital
and use win-streak doubling strategy to either graduate at $100 profit or restart on $0.30 loss.

Features:
- Individual agent lifecycle management (birth/death/graduation)
- Win-based lot size doubling (0.01 → 0.02 → 0.04 → 0.08...)
- Zone-based entry signals using breakout strategy logic
- Virtual balance tracking for backtesting with agent restarts
- $0.30 take profit per trade, no individual stop losses
- Full integration with TradingBot BaseStrategy architecture

Author: Augment Code
Purpose: Advanced algorithmic trading for positive impact
"""

import logging
import MetaTrader5 as mt5
from datetime import datetime, timedelta
from typing import List, Tuple, Dict, Any, Optional
from .base_strategy import BaseStrategy

log = logging.getLogger("ZoneBoS.SwarnStrategy")

class SwarnStrategy(BaseStrategy):
    """Swarm trading strategy with agent lifecycle management and win-streak doubling.

    This strategy implements a sophisticated swarm-based trading system where agents:
    - Start with $0.30 virtual capital
    - Use win-streak based lot size doubling (0.01 → 0.02 → 0.04 → 0.08...)
    - Graduate at $100 profit or die at $0.30 loss
    - Use zone-based entry signals from breakout strategy logic
    - Have fixed $0.30 take profit with no stop losses
    """

    def __init__(self, config):
        super().__init__(config)

        # Merge strategy config with base config
        strategy_config = self.get_strategy_config()
        for key, value in strategy_config.items():
            setattr(self.config, key, value)

        # Agent state
        self.agent_id = 1
        self.agent_status = "active"  # active/dead/graduated
        self.starting_balance = self.config.SWARM_STARTING_CAPITAL
        self.virtual_balance = self.config.SWARM_STARTING_CAPITAL  # For backtesting
        self.current_generation = 1

        # Win-streak tracking
        self.win_streak = 0
        self.max_win_streak = 0
        self.current_lot_size = self.config.SWARM_BASE_LOT

        # Trade statistics
        self.total_trades = 0
        self.total_wins = 0
        self.total_losses = 0
        self.agent_start_time = datetime.now()

        # Zone-based trading (similar to breakout strategy)
        self.zones = []
        self.last_zone_refresh_time = None
        self.confirmation_tracker = {"confirmations": {}}

        # Agent lifecycle tracking
        self.agent_generations = []  # Track multiple agent lifecycles for backtesting

        self.logger.info(f"SwarnStrategy initialized: Agent {self.agent_id}")

    def get_strategy_config(self) -> Dict[str, Any]:
        """Return swarm strategy configuration."""
        return {
            # Swarm-specific parameters
            "SWARM_STARTING_CAPITAL": 0.30,      # Starting capital per agent
            "SWARM_GRADUATION_TARGET": 100.0,    # Profit target for graduation
            "SWARM_TP_FIXED": 0.30,             # Take profit per trade
            "SWARM_BASE_LOT": 0.01,             # Base lot size
            "SWARM_MAX_LOT_MULTIPLIER": 10,     # Max doubling steps (2^10 = 1024x)

            # Trade Settings (override base strategy defaults)
            "BASE_LOT_SIZE": 0.01,
            "SL_POINTS": 0.0,    # No stop losses
            "TP_POINTS": 0.30,   # Fixed TP

            # Disable martingale - we use win-based doubling instead
            "ENABLE_MARTINGALE": False,

            # Zone Settings (borrowed from breakout strategy)
            "ZONE_GAP": 2.50,                    # Distance between zones
            "ZONE_REVERSAL_THRESHOLD": 1.55,    # Price distance to reverse a zone
            "ZONE_CONFIRMATION_CANDLES": 1,     # Immediate breakout confirmation
            "BASE_GENERATION_PRICE": 3000.0,    # Stable price to anchor zone generation
            "ZONE_REFRESH_INTERVAL": 60,        # seconds to update zone classifications
            "ZONE_COOLDOWN_SECONDS": 300,       # 5 minutes cooldown before zone reactivation
            "TRADE_TYPE_COOLDOWN_SECONDS": 1800, # 30 minutes cooldown for same trade type
        }

    def validate_config(self, config_dict: Dict[str, Any]) -> None:
        """Validate swarm strategy configuration."""
        # Swarm-specific validations
        if config_dict.get('SWARM_STARTING_CAPITAL', 0) <= 0:
            raise ValueError("SWARM_STARTING_CAPITAL must be greater than 0")
        if config_dict.get('SWARM_GRADUATION_TARGET', 0) <= 0:
            raise ValueError("SWARM_GRADUATION_TARGET must be greater than 0")
        if config_dict.get('SWARM_TP_FIXED', 0) <= 0:
            raise ValueError("SWARM_TP_FIXED must be greater than 0")
        if config_dict.get('SWARM_BASE_LOT', 0) <= 0:
            raise ValueError("SWARM_BASE_LOT must be greater than 0")
        if config_dict.get('SWARM_MAX_LOT_MULTIPLIER', 0) <= 0:
            raise ValueError("SWARM_MAX_LOT_MULTIPLIER must be greater than 0")

        # Zone-specific validations (borrowed from breakout strategy)
        if config_dict.get('ZONE_GAP', 1) <= 0:
            raise ValueError("ZONE_GAP must be greater than 0")
        if config_dict.get('BASE_GENERATION_PRICE', 1) <= 0:
            raise ValueError("BASE_GENERATION_PRICE must be greater than 0")

    def initialize(self, current_price: float, historical_data=None) -> None:
        """Initialize the swarm strategy with zones and agent state."""
        self.logger.info("Initializing swarm strategy...")

        # Initialize agent state
        self.starting_balance = self.config.SWARM_STARTING_CAPITAL
        self.virtual_balance = self.config.SWARM_STARTING_CAPITAL
        self.current_lot_size = self.config.SWARM_BASE_LOT
        self.agent_status = "active"
        self.win_streak = 0
        self.total_trades = 0
        self.total_wins = 0
        self.total_losses = 0
        self.agent_start_time = datetime.now()

        # Initialize zones (similar to breakout strategy)
        self.zones = self._create_infinite_zones(
            current_price,
            self.config.ZONE_GAP,
            self.config.BASE_GENERATION_PRICE
        )

        # Update zone classifications based on current price
        self.zones = self._update_zone_classification(self.zones, current_price)

        self.logger.info(f"Initialized {len(self.zones)} zones around current price {current_price:.2f}")
        self.logger.info(f"Agent starting balance: ${self.starting_balance:.2f}")

        # Update refresh time
        if historical_data is not None and not historical_data.empty:
            self.last_zone_refresh_time = historical_data.index[-1]
        else:
            self.last_zone_refresh_time = datetime.now()

    def update(self, current_price: float, current_time: Optional[datetime] = None) -> None:
        """Update strategy state with current market conditions."""
        if current_time is None:
            current_time = datetime.now()

        # 1. Check for zone reversals based on the latest price
        self.zones = self._check_zone_reversal(self.zones, current_price, self.config.ZONE_REVERSAL_THRESHOLD)

        # 2. Reactivate zones after cooldown period
        cooldown_seconds = getattr(self.config, 'ZONE_COOLDOWN_SECONDS', 300)
        self.zones = self._reactivate_zones_after_cooldown(self.zones, current_time, cooldown_seconds)

        # 3. Update zone classifications (support/resistance)
        refresh_interval = self.config.ZONE_REFRESH_INTERVAL
        if self.last_zone_refresh_time is None or (current_time.timestamp() - self.last_zone_refresh_time.timestamp()) >= refresh_interval:
            self.zones = self._update_zone_classification(self.zones, current_price)
            self.last_zone_refresh_time = current_time
            self.logger.debug(f"Updated zone classifications for current price: {current_price:.2f}")

    def get_entry_signals(self, tick_price: float, prev_tick_price: Optional[float] = None,
                         current_time: Optional[datetime] = None) -> List[Tuple[str, Dict[str, Any]]]:
        """Generate entry signals based on zone breakouts and reversals."""
        return self._evaluate_entry_signal_with_tick_price(tick_price, prev_tick_price, current_time)

    def get_position_size(self, signal_data: Dict[str, Any], trade_history: List[Dict]) -> float:
        """Return current lot size based on win streak."""
        return self.current_lot_size

    def get_stop_loss(self, signal_type: str, entry_price: float, signal_data: Dict[str, Any]) -> float:
        """Return 0 for no stop loss (swarm strategy uses no SL)."""
        return 0.0

    def get_take_profit(self, signal_type: str, entry_price: float, signal_data: Dict[str, Any]) -> float:
        """Return fixed take profit based on swarm strategy."""
        if signal_type == "Buy":
            return entry_price + self.config.SWARM_TP_FIXED
        else:  # Sell
            return entry_price - self.config.SWARM_TP_FIXED

    def on_trade_opened(self, trade_data: Dict[str, Any]) -> None:
        """Called when a trade is successfully opened."""
        self.logger.info(f"Agent {self.agent_id} OPENED: {trade_data.get('type', 'Unknown')} @ {trade_data.get('entry_price', 0):.2f} | "
                        f"Lot: {trade_data.get('lot', 0):.2f} | TP: {trade_data.get('tp', 0):.2f} | "
                        f"Win Streak: {self.win_streak}")

    def on_trade_closed(self, trade_data: Dict[str, Any]) -> None:
        """Called when a trade is closed - update win streak and lot size."""
        profit = trade_data.get('profit', 0)
        trade_outcome = "Win" if profit > 0 else "Loss"

        # Update lot size based on trade outcome
        self.update_lot_size(trade_outcome)

        # Update virtual balance for backtesting
        if hasattr(self, 'virtual_balance') and self.virtual_balance is not None:
            self.virtual_balance += profit

        self.logger.info(f"Agent {self.agent_id} CLOSED: {trade_outcome} | "
                        f"Profit: ${profit:.2f} | Win Streak: {self.win_streak} | "
                        f"Next Lot: {self.current_lot_size:.2f}")

    # --- Agent Lifecycle Management ---

    def update_lot_size(self, trade_outcome: str) -> None:
        """Update lot size based on trade outcome using win-streak doubling."""
        if trade_outcome == "Win":
            self.win_streak += 1
            self.total_wins += 1

            # Double lot size (with safety limit)
            if self.win_streak <= self.config.SWARM_MAX_LOT_MULTIPLIER:
                self.current_lot_size = self.config.SWARM_BASE_LOT * (2 ** self.win_streak)
            else:
                # Cap at maximum multiplier
                self.current_lot_size = self.config.SWARM_BASE_LOT * (2 ** self.config.SWARM_MAX_LOT_MULTIPLIER)

            self.max_win_streak = max(self.max_win_streak, self.win_streak)

            self.logger.info(f"Agent {self.agent_id} WIN STREAK {self.win_streak}: "
                           f"Next lot size = {self.current_lot_size:.2f}")

        else:  # Loss
            if self.win_streak > 0:
                self.logger.info(f"Agent {self.agent_id} LOSS: Win streak broken at {self.win_streak}, "
                               f"resetting to base lot {self.config.SWARM_BASE_LOT}")

            self.win_streak = 0
            self.current_lot_size = self.config.SWARM_BASE_LOT
            self.total_losses += 1

        self.total_trades += 1

    def get_current_balance(self) -> float:
        """Get current balance (virtual for backtesting, real for live trading)."""
        if hasattr(self, 'virtual_balance') and self.virtual_balance is not None:
            return self.virtual_balance

        # For live trading, get MT5 balance
        try:
            account_info = mt5.account_info()
            if account_info:
                return account_info.balance
            else:
                self.logger.error("Failed to get account info")
                return self.starting_balance or 0.0
        except Exception as e:
            self.logger.error(f"Error getting balance: {e}")
            return self.starting_balance or 0.0

    def check_agent_status(self) -> str:
        """Check if agent should die, graduate, or continue trading."""
        current_balance = self.get_current_balance()
        profit_loss = current_balance - self.starting_balance

        if profit_loss <= -self.config.SWARM_STARTING_CAPITAL:
            self.agent_status = "dead"
            self.logger.warning(f"Agent {self.agent_id} DIED: Lost ${self.config.SWARM_STARTING_CAPITAL:.2f} "
                               f"(Balance: ${current_balance:.2f})")
            return "dead"
        elif profit_loss >= self.config.SWARM_GRADUATION_TARGET:
            self.agent_status = "graduated"
            self.logger.info(f"Agent {self.agent_id} GRADUATED: Reached ${self.config.SWARM_GRADUATION_TARGET:.2f} profit! "
                            f"(Balance: ${current_balance:.2f})")
            return "graduated"

        return "active"

    def restart_agent(self) -> None:
        """Restart agent after death with fresh capital (for backtesting)."""
        # Save current generation stats
        generation_stats = self.get_agent_statistics()
        generation_stats["generation"] = self.current_generation
        generation_stats["end_reason"] = "death"
        self.agent_generations.append(generation_stats)

        self.logger.info(f"Agent {self.agent_id} Generation {self.current_generation} ended: "
                        f"Trades={self.total_trades}, Win Rate={generation_stats['win_rate']:.1f}%")

        # Reset agent state
        self.current_generation += 1
        self.virtual_balance = self.config.SWARM_STARTING_CAPITAL
        self.starting_balance = self.config.SWARM_STARTING_CAPITAL
        self.current_lot_size = self.config.SWARM_BASE_LOT
        self.win_streak = 0
        self.agent_status = "active"
        self.total_trades = 0
        self.total_wins = 0
        self.total_losses = 0
        self.max_win_streak = 0
        self.agent_start_time = datetime.now()

        self.logger.info(f"Agent {self.agent_id} Generation {self.current_generation} started with ${self.virtual_balance:.2f}")

    def get_agent_statistics(self) -> Dict[str, Any]:
        """Get comprehensive agent statistics."""
        current_balance = self.get_current_balance()
        profit_loss = current_balance - self.starting_balance
        runtime = datetime.now() - self.agent_start_time

        win_rate = (self.total_wins / max(self.total_trades, 1)) * 100

        return {
            "agent_id": self.agent_id,
            "status": self.agent_status,
            "current_balance": current_balance,
            "profit_loss": profit_loss,
            "total_trades": self.total_trades,
            "total_wins": self.total_wins,
            "total_losses": self.total_losses,
            "win_rate": win_rate,
            "current_win_streak": self.win_streak,
            "max_win_streak": self.max_win_streak,
            "current_lot_size": self.current_lot_size,
            "runtime": runtime,
            "generation": getattr(self, 'current_generation', 1)
        }

    # --- Zone Management Methods (borrowed from BreakoutStrategy) ---

    def _create_infinite_zones(self, current_price: float, zone_gap: float, base_generation_price: float) -> List[Dict]:
        """Create unlimited fixed-interval zones around a base generation price."""
        zones = []

        # Generate a large, but finite, number of zones for practical purposes
        num_zones_per_side = 500

        # Generate zones from the base price to ensure consistency
        for i in range(-num_zones_per_side, num_zones_per_side + 1):
            zone_price = base_generation_price + (i * zone_gap)

            zone = {
                'price': zone_price,
                'zone_type': 'support' if zone_price < current_price else 'resistance',
                'is_reversed': False,
                'last_break_time': None,
                'break_count': 0,
                'is_active': True,  # A zone is active and can be traded
                'deactivated_time': None,  # Track when zone was deactivated for cooldown
                'last_buy_trade_time': None,   # Track last buy trade time for cooldown
                'last_sell_trade_time': None   # Track last sell trade time for cooldown
            }
            zones.append(zone)

        zones.sort(key=lambda z: z['price'])
        self.logger.info(f"Created {len(zones)} fixed-interval zones from base price {base_generation_price:.2f}")
        return zones

    def _update_zone_classification(self, zones: List[Dict], current_price: float) -> List[Dict]:
        """Update zone classification based on current price, respecting reversals."""
        for zone in zones:
            # If a zone is reversed, its type is fixed until reversed back
            if zone.get('is_reversed', False):
                continue

            # Classify based on price relative to the zone
            if zone['price'] > current_price:
                zone['zone_type'] = 'resistance'
            else:
                zone['zone_type'] = 'support'

        return zones

    def _check_zone_reversal(self, zones: List[Dict], current_price: float, reversal_threshold: float) -> List[Dict]:
        """Check if zones should be reversed or restored based on price movement."""
        for zone in zones:
            zone_price = zone['price']
            is_reversed = zone.get('is_reversed', False)

            if not is_reversed:
                # Logic to reverse a normal zone
                if zone['zone_type'] == 'resistance' and current_price > zone_price + reversal_threshold:
                    # Resistance flips to Support
                    zone['zone_type'] = 'support'
                    zone['is_reversed'] = True
                    zone['last_break_time'] = datetime.now()
                    zone['break_count'] += 1
                    zone['is_active'] = True # Reactivate the zone for new trades
                    self.logger.info(f"Resistance at {zone_price:.2f} REVERSED to Support")

                elif zone['zone_type'] == 'support' and current_price < zone_price - reversal_threshold:
                    # Support flips to Resistance
                    zone['zone_type'] = 'resistance'
                    zone['is_reversed'] = True
                    zone['last_break_time'] = datetime.now()
                    zone['break_count'] += 1
                    zone['is_active'] = True # Reactivate the zone for new trades
                    self.logger.info(f"Support at {zone_price:.2f} REVERSED to Resistance")

            else:
                # Logic to restore a reversed zone
                if zone['zone_type'] == 'support' and current_price < zone_price - reversal_threshold:
                    # A reversed resistance (now support) restores to resistance
                    zone['zone_type'] = 'resistance'
                    zone['is_reversed'] = False
                    zone['is_active'] = True  # Reactivate the zone
                    self.logger.info(f"Reversed zone at {zone_price:.2f} RESTORED to Resistance")

                elif zone['zone_type'] == 'resistance' and current_price > zone_price + reversal_threshold:
                    # A reversed support (now resistance) restores to support
                    zone['zone_type'] = 'support'
                    zone['is_reversed'] = False
                    zone['is_active'] = True  # Reactivate the zone
                    self.logger.info(f"Reversed zone at {zone_price:.2f} RESTORED to Support")

        return zones

    def _reactivate_zones_after_cooldown(self, zones: List[Dict], current_time: datetime, cooldown_seconds: int) -> List[Dict]:
        """Reactivate zones after cooldown period."""
        for zone in zones:
            if not zone.get('is_active', True) and zone.get('deactivated_time'):
                time_since_deactivation = (current_time - zone['deactivated_time']).total_seconds()
                if time_since_deactivation >= cooldown_seconds:
                    zone['is_active'] = True
                    zone['deactivated_time'] = None
                    self.logger.debug(f"Zone at {zone['price']:.2f} REACTIVATED after cooldown")

        return zones

    def _is_trade_type_allowed(self, zone: Dict, trade_type: str, current_time: datetime, cooldown_seconds: int) -> bool:
        """Check if trade type is allowed based on cooldown."""
        if trade_type == "Buy":
            last_trade_time = zone.get('last_buy_trade_time')
        else:  # Sell
            last_trade_time = zone.get('last_sell_trade_time')

        if last_trade_time is None:
            return True

        time_since_last_trade = (current_time - last_trade_time).total_seconds()
        return time_since_last_trade >= cooldown_seconds

    def _update_trade_type_timestamp(self, zone: Dict, trade_type: str, current_time: datetime) -> None:
        """Update the timestamp for the last trade of this type on this zone."""
        if trade_type == "Buy":
            zone['last_buy_trade_time'] = current_time
        else:  # Sell
            zone['last_sell_trade_time'] = current_time

    def _evaluate_entry_signal_with_tick_price(self, tick_price: float, prev_tick_price: Optional[float] = None,
                                              current_time: Optional[datetime] = None) -> List[Tuple[str, Dict[str, Any]]]:
        """Evaluate if there's an entry signal based on price crossing a zone or reversing from it."""
        if prev_tick_price is None:
            return []

        if self.confirmation_tracker is None:
            self.confirmation_tracker = {"confirmations": {}}

        confirmation_required = getattr(self.config, 'ZONE_CONFIRMATION_CANDLES', 1)
        trade_type_cooldown = getattr(self.config, 'TRADE_TYPE_COOLDOWN_SECONDS', 1800)
        signals = []

        for zone in self.zones:
            if not zone.get('is_active', True):
                continue

            zone_price = zone['price']
            zone_type = zone['zone_type']

            # 1. BREAKOUT SIGNALS: Price crossing through the zone
            crossed_up = prev_tick_price <= zone_price < tick_price
            crossed_down = prev_tick_price >= zone_price > tick_price

            breakout_signal = None
            if zone_type == 'resistance' and crossed_up:
                breakout_signal = "Buy"
            elif zone_type == 'support' and crossed_down:
                breakout_signal = "Sell"

            # Process breakout signals only
            entry_signal = breakout_signal
            signal_type = "breakout"

            if entry_signal:
                # Check trade type cooldown
                if not self._is_trade_type_allowed(zone, entry_signal, current_time or datetime.now(), trade_type_cooldown):
                    self.logger.debug(f"Zone at {zone_price:.2f} {entry_signal} signal blocked - trade type cooldown active")
                    continue

                # Check confirmation
                zone_key = f"{zone_type}_{zone_price:.2f}_{signal_type}"

                if confirmation_required <= 1:
                    # Immediate signal
                    pass
                else:
                    # Start confirmation tracking
                    self.confirmation_tracker["confirmations"][zone_key] = self.confirmation_tracker["confirmations"].get(zone_key, 0) + 1
                    if self.confirmation_tracker["confirmations"][zone_key] < confirmation_required:
                        continue  # Not enough confirmation yet
                    self.confirmation_tracker["confirmations"][zone_key] = 0  # Reset after triggering

                # Create signal
                signal_data = {"type": zone_type, "zone": zone, "signal_type": signal_type}
                signals.append((entry_signal, signal_data))

                # Update trade type timestamp
                self._update_trade_type_timestamp(zone, entry_signal, current_time or datetime.now())

                # For breakouts, deactivate the zone completely
                zone['is_active'] = False
                zone['deactivated_time'] = current_time or datetime.now()
                self.logger.info(f"Zone at {zone['price']:.2f} DEACTIVATED after {signal_type} signal for a {entry_signal}.")

        # Clean up stale confirmations
        active_keys = {f"{z['zone_type']}_{z['price']:.2f}" for z in self.zones}
        stale_keys = [k for k in self.confirmation_tracker["confirmations"] if k not in active_keys]
        for k in stale_keys:
            del self.confirmation_tracker["confirmations"][k]

        return signals


# --- Factory Function ---

def get_strategy_instance(config):
    """Factory function to create a SwarnStrategy instance."""
    return SwarnStrategy(config)                    