# How to Create and Implement a New Trading Strategy

This document outlines the process for creating a new trading strategy and integrating it into the trading bot. The system is designed to be plug-and-play, allowing you to focus on the strategy logic itself while reusing the core infrastructure for order execution, data gathering, visualization, and backtesting.

## 🏗️ Strategy Architecture Overview

The trading bot now uses a **flexible, class-based strategy architecture** that allows you to create any type of trading strategy:

- **Zone-based strategies** (like the breakout strategy)
- **Indicator-based strategies** (moving averages, RSI, MACD, etc.)
- **Pattern recognition strategies**
- **Machine learning strategies**
- **Custom hybrid approaches**

The core trading engine (`core/base_trader.py`) dynamically loads your strategy and provides:
- ✅ **Order Execution**: Complete broker abstraction (MT5, future brokers)
- ✅ **Data Access**: Historical data, real-time prices, position management
- ✅ **Backtesting**: Full backtesting engine with synthetic tick generation
- ✅ **Visualization**: Automatic chart generation and trade history
- ✅ **Risk Management**: Position limits, profit targets, error handling

## 📚 Strategy Examples

### 1. Zone-Based Strategy: `breakout.py`
- Creates fixed-interval zones around price levels
- Trades breakouts through zones and reversals off zones
- Includes sophisticated zone management and martingale position sizing

### 2. Indicator-Based Strategy: `bollinger.py`
- Uses Bollinger Bands for mean reversion and trend following
- No zone concepts - purely indicator-driven
- Demonstrates non-zone strategy implementation

### 3. Swarm-Based Strategy: `swarn.py`
- Sophisticated agent lifecycle management with graduation/death mechanics
- Win-streak based lot size doubling (0.01 → 0.02 → 0.04 → 0.08...)
- Zone-based entry signals with fixed $0.30 take profit
- Virtual balance tracking for backtesting with agent restarts

## 📊 SwarnStrategy Detailed Documentation

### Overview
The SwarnStrategy implements a sophisticated swarm-based trading system where individual agents:
- Start with $0.30 virtual capital
- Use win-streak based lot size doubling
- Graduate at $100 profit or die at $0.30 loss
- Use zone-based entry signals with fixed $0.30 take profit

### Key Features

#### Agent Lifecycle Management
- **Birth**: Agent starts with $0.30 capital
- **Growth**: Win-streak doubling increases lot sizes exponentially
- **Death**: Agent dies when losing $0.30 (full starting capital)
- **Graduation**: Agent graduates when reaching $100 profit
- **Restart**: In backtesting, dead agents restart with fresh capital

#### Win-Streak Doubling System
```
Win Streak 0: 0.01 lot
Win Streak 1: 0.02 lot (2^1 * base)
Win Streak 2: 0.04 lot (2^2 * base)
Win Streak 3: 0.08 lot (2^3 * base)
...
Win Streak 10: 10.24 lot (capped at max multiplier)
```

#### Zone-Based Trading
- Uses breakout strategy's zone logic for entry signals
- Fixed zones with 2.50 point gaps
- Breakout signal detection and zone type reversal
- Zone cooldown and reactivation mechanisms

### Configuration Parameters

```python
{
    # Swarm-specific parameters
    "SWARM_STARTING_CAPITAL": 0.30,      # Starting capital per agent
    "SWARM_GRADUATION_TARGET": 100.0,    # Profit target for graduation
    "SWARM_TP_FIXED": 0.30,             # Take profit per trade
    "SWARM_BASE_LOT": 0.01,             # Base lot size
    "SWARM_MAX_LOT_MULTIPLIER": 10,     # Max doubling steps (2^10 = 1024x)

    # Zone Settings (inherited from breakout strategy)
    "ZONE_GAP": 2.50,                    # Distance between zones
    "ZONE_REVERSAL_THRESHOLD": 1.55,    # Price distance to reverse a zone
    "ZONE_CONFIRMATION_CANDLES": 1,     # Immediate breakout confirmation
    "BASE_GENERATION_PRICE": 3000.0,    # Stable price to anchor zone generation
    "ZONE_REFRESH_INTERVAL": 60,        # seconds to update zone classifications
    "ZONE_COOLDOWN_SECONDS": 300,       # 5 minutes cooldown before zone reactivation
    "TRADE_TYPE_COOLDOWN_SECONDS": 1800, # 30 minutes cooldown for same trade type
}
```

### Usage Examples

#### Backtesting
```bash
python mainBot.py --strategy swarn --backtest --days 10
```

#### Live Trading
```bash
python mainBot.py --strategy swarn --live
```

#### Custom Configuration
```python
from strategies.swarn import get_strategy_instance
from config import Config

config = Config()
# Modify swarm parameters
config.SWARM_STARTING_CAPITAL = 0.50  # Start with $0.50
config.SWARM_GRADUATION_TARGET = 200.0  # Graduate at $200

strategy = get_strategy_instance(config)
```

### Agent Statistics Tracking

The strategy tracks comprehensive statistics:
- Agent lifecycle (active/dead/graduated)
- Win/loss counts and win rate
- Current and maximum win streaks
- Virtual balance for backtesting
- Generation tracking for multiple agent lifecycles
- Trade history with agent-specific metadata

### Backtest Results Example

```
SWARM AGENT BACKTEST SUMMARY
Agent ID: 1
Final Status: ACTIVE
Total Generations: 2
Starting Capital: $0.30
Final Balance: $45.60
Total Profit/Loss: +$45.30

OVERALL PERFORMANCE:
Total Trades: 127
Total Wins: 89
Total Losses: 38
Overall Win Rate: 70.1%

GENERATION BREAKDOWN:
Generation  1: Trades= 67 | Win Rate= 65.7% | Max Streak= 8 | P&L=  -$0.30 | End: death
Generation  2: Trades= 60 | Win Rate= 75.0% | Max Streak=12 | P&L= +$45.60 | End: active
```

## 🚀 Creating a New Strategy

### Step 1: Create Your Strategy Class

Create a new Python file in the `strategies` directory (e.g., `my_strategy.py`) and inherit from `BaseStrategy`:

```python
from .base_strategy import BaseStrategy
from typing import List, Tuple, Dict, Any, Optional
from datetime import datetime

class MyStrategy(BaseStrategy):
    def __init__(self, config):
        super().__init__(config)
        # Initialize your strategy-specific variables
        self.my_indicator = []
        self.last_signal_time = None
```

### Step 2: Implement Required Abstract Methods

Your strategy class **must** implement these abstract methods:

#### Configuration Methods:
```python
def get_strategy_config(self) -> Dict[str, Any]:
    """Return strategy-specific configuration parameters."""
    return {
        "BASE_LOT_SIZE": 0.01,
        "SL_POINTS": 5.0,
        "TP_POINTS": 10.0,
        # Add your custom parameters here
        "MY_INDICATOR_PERIOD": 14,
        "MY_THRESHOLD": 0.5,
    }

def validate_config(self, config_dict: Dict[str, Any]) -> None:
    """Validate your strategy configuration."""
    if config_dict.get('MY_INDICATOR_PERIOD', 1) <= 0:
        raise ValueError("MY_INDICATOR_PERIOD must be greater than 0")
    # Add more validation as needed
```

#### Core Strategy Methods:
```python
def initialize(self, current_price: float, historical_data=None) -> None:
    """Initialize your strategy state."""
    # Set up indicators, load models, initialize variables
    if historical_data is not None:
        self._calculate_initial_indicators(historical_data)

def update(self, current_price: float, current_time: Optional[datetime] = None) -> None:
    """Update strategy state with new market data."""
    # Update indicators, recalculate signals, manage state
    self._update_indicators(current_price)

def get_entry_signals(self, tick_price: float, prev_tick_price: Optional[float] = None,
                     current_time: Optional[datetime] = None) -> List[Tuple[str, Dict[str, Any]]]:
    """Generate entry signals - THIS IS YOUR MAIN STRATEGY LOGIC."""
    signals = []

    # Your signal generation logic here
    if self._should_buy(tick_price):
        signal_data = {"reason": "my_buy_condition", "strength": 0.8}
        signals.append(("Buy", signal_data))
    elif self._should_sell(tick_price):
        signal_data = {"reason": "my_sell_condition", "strength": 0.9}
        signals.append(("Sell", signal_data))

    return signals
```

### Step 3: Add Factory Function

Add this at the end of your strategy file:

```python
def get_strategy_instance(config):
    """Factory function to create your strategy instance."""
    return MyStrategy(config)
```

### Step 4: Run Your Strategy

**Live Trading:**
```bash
python mainBot.py --strategy my_strategy
```

**Backtesting:**
```bash
python mainBot.py --backtest --strategy my_strategy --days 10 --save-results
```

## 🔧 Optional Methods You Can Override

The `BaseStrategy` class provides default implementations for these methods, but you can override them for custom behavior:

```python
def get_exit_signals(self, open_positions: List[Dict], current_price: float,
                    current_time: Optional[datetime] = None) -> List[Tuple[str, Dict]]:
    """Custom exit logic beyond standard SL/TP."""
    return []

def on_trade_opened(self, trade_data: Dict[str, Any]) -> None:
    """Called when a trade is successfully opened."""
    pass

def on_trade_closed(self, trade_data: Dict[str, Any]) -> None:
    """Called when a trade is closed."""
    pass

def get_position_size(self, signal_data: Dict[str, Any], trade_history: List[Dict]) -> float:
    """Custom position sizing logic."""
    return self.config.BASE_LOT_SIZE

def get_stop_loss(self, signal_type: str, entry_price: float, signal_data: Dict[str, Any]) -> float:
    """Custom stop loss calculation."""
    sl_points = self.config.SL_POINTS
    return entry_price - sl_points if signal_type == "Buy" else entry_price + sl_points

def get_take_profit(self, signal_type: str, entry_price: float, signal_data: Dict[str, Any]) -> float:
    """Custom take profit calculation."""
    tp_points = self.config.TP_POINTS
    return entry_price + tp_points if signal_type == "Buy" else entry_price - tp_points
```

## 📋 Strategy Development Checklist

- [ ] Create strategy class inheriting from `BaseStrategy`
- [ ] Implement `get_strategy_config()` with your parameters
- [ ] Implement `validate_config()` with proper validation
- [ ] Implement `initialize()` to set up your strategy state
- [ ] Implement `update()` to maintain strategy state
- [ ] Implement `get_entry_signals()` with your trading logic
- [ ] Add `get_strategy_instance()` factory function
- [ ] Test with backtesting: `python mainBot.py --backtest --strategy your_strategy`
- [ ] Verify configuration validation works
- [ ] Test live trading with small position sizes

## 🎯 Key Benefits of New Architecture

1. **Complete Freedom**: No forced zone concepts - implement any strategy type
2. **Reuse Infrastructure**: Focus on strategy logic, not plumbing
3. **Easy Testing**: Full backtesting and visualization automatically available
4. **Type Safety**: Proper type hints and abstract method enforcement
5. **Extensible**: Override only what you need, use defaults for the rest
6. **Backward Compatible**: Old function-based strategies still work

## 🔄 Migration from Old Function-Based Strategies

If you have an old function-based strategy, you can easily convert it:

1. Create a class inheriting from `BaseStrategy`
2. Move your `strategy_config` dict to `get_strategy_config()` method
3. Move your validation to `validate_config()` method
4. Move your signal logic to `get_entry_signals()` method
5. Add the factory function

The old function-based approach is still supported for backward compatibility.

## 📊 Optional Display Methods

Strategies can implement these optional methods to customize their live trading output:

```python
def get_display_info(self, current_price: float) -> str:
    """Get strategy display information for live trading output."""
    # Example implementation
    return f"🎯 MY STRATEGY | Price: {current_price:.2f} | Status: Active"
    
def get_signal_description(self, side: str, signal_data: Dict[str, Any], current_price: float) -> str:
    """Get custom signal description for logging."""
    # Example implementation
    signal_type = signal_data.get('signal_type', 'unknown')
    return f"🚀 MY SIGNAL | {side.upper()} | Price: {current_price:.2f} | Type: {signal_type}"
```

## 📊 Live Trading Output Customization

### Default Behavior
If a strategy doesn't implement the optional display methods, the base trader will show:
```
📊 StrategyName | Price: 2650.45
📊 SIGNAL | BUY | Price: 2650.45 | Type: breakout
```

### Custom Display Examples

#### Breakout Strategy
```python
def get_display_info(self, current_price: float) -> str:
    nearest_support, nearest_resistance = self._get_nearest_zones(current_price)
    support_str = f"Support: {nearest_support:.2f}" if nearest_support else "Support: N/A"
    resistance_str = f"Resistance: {nearest_resistance:.2f}" if nearest_resistance else "Resistance: N/A"
    active_zones = len([z for z in self.zones if z.get('is_active', True)])
    
    return (f"🎯 BREAKOUT STRATEGY | Price: {current_price:.2f} | "
            f"{support_str} | {resistance_str} | "
            f"Zones: {active_zones}/{len(self.zones)} active")

def get_signal_description(self, side: str, signal_data: Dict[str, Any], current_price: float) -> str:
    signal_type = signal_data.get('signal_type', 'unknown')
    zone_info = signal_data.get('zone', {})
    zone_price = zone_info.get('price', 0)
    zone_type = signal_data.get('type', 'unknown')
    
    if signal_type == 'breakout':
        return (f"🚀 BREAKOUT SIGNAL | {side.upper()} | Price: {current_price:.2f} | "
                f"Zone: {zone_price:.2f} ({zone_type}) | Action: Breaking through zone")
    elif signal_type == 'reversal':
        return (f"🔄 REVERSAL SIGNAL | {side.upper()} | Price: {current_price:.2f} | "
                f"Zone: {zone_price:.2f} ({zone_type}) | Action: Bouncing off zone")
    else:
        return f"📊 BREAKOUT SIGNAL | {side.upper()} | Price: {current_price:.2f} | Type: {signal_type}"
```

#### Bollinger Bands Strategy
```python
def get_display_info(self, current_price: float) -> str:
    if self.bb_data['upper']:
        upper = self.bb_data['upper'][-1]
        middle = self.bb_data['middle'][-1]
        lower = self.bb_data['lower'][-1]
        width = self.bb_data['width'][-1]
        position = self.bb_data['position'][-1]
        
        position_str = "Above Upper" if position > 0.8 else "Below Lower" if position < 0.2 else "Middle"
        
        return (f"📈 BOLLINGER BANDS | Price: {current_price:.2f} | "
                f"Upper: {upper:.2f} | Middle: {middle:.2f} | Lower: {lower:.2f} | "
                f"Width: {width:.2f} | Position: {position_str}")
    else:
        return f"📈 BOLLINGER BANDS | Price: {current_price:.2f} | Bands: Calculating..."

def get_signal_description(self, side: str, signal_data: Dict[str, Any], current_price: float) -> str:
    signal_type = signal_data.get('signal_type', 'unknown')
    reason = signal_data.get('reason', 'unknown')
    
    if 'upper_band' in signal_type:
        return f"📈 BOLLINGER SIGNAL | {side.upper()} | Price: {current_price:.2f} | Type: Upper Band {reason}"
    elif 'lower_band' in signal_type:
        return f"📈 BOLLINGER SIGNAL | {side.upper()} | Price: {current_price:.2f} | Type: Lower Band {reason}"
    else:
        return f"📈 BOLLINGER SIGNAL | {side.upper()} | Price: {current_price:.2f} | Type: {signal_type}"
```

## Benefits of Custom Display

1. **Cleaner Output**: Replace spammy tick statistics with meaningful strategy information
2. **Strategy-Specific Information**: Show support/resistance levels, indicators, zone status
3. **Better Monitoring**: Clear visual feedback about strategy state and signals
4. **Extensible Framework**: Easy to add new display features without modifying base trader
5. **Consistent Interface**: Standard format across all strategies while allowing customization

## Display Guidelines

- Use emojis for easy visual identification (🎯 🚀 🔄 📈 📊)
- Include current price as a reference point
- Show the most important strategy-specific information
- Keep information concise but informative
- Use consistent formatting across similar signal types

## Architecture Benefits

- **Separation of Concerns**: Base trader handles infrastructure, strategies handle display
- **Extensibility**: Easy to add new strategies with custom display logic
- **Maintainability**: Changes to display logic don't affect base trader
- **Flexibility**: Each strategy can show exactly what's most relevant to its approach