import logging

# ───────────────────────────── SIMPLIFIED CONFIGURATION ────────────────────────────────

class Config:
    # Trading Symbol and Data
    SYMBOL = "XAUUSD"
    LOOKBACK_DAYS = 30
    BACKTEST_DAYS = 10
    TIMEFRAME = "M1"  # Default timeframe: M1, M5, M15, M30, H1, H4, D1

    # MT5 Connection
    LOGIN = 14293416
    PASSWORD = "Reshideqk1@"  # Change this for real trading
    SERVER = "Headway-Real"

    # Alternative: Use environment variables for security
    # PASSWORD = os.getenv('MT5_PASSWORD', 'BW3!5nb9')
    # LOGIN = int(os.getenv('MT5_LOGIN', '13759858'))
    # SERVER = os.getenv('MT5_SERVER', 'Headway-Real')
    
    # Profit/Loss Calculation
    PROFIT_LOSS_MULTIPLIER = 100.0 # For XAUUSD, this is typically 100

    # Trading Strategy Timing
    STRATEGY_INTERVAL = 1  # seconds (tick-based checking)

    # Tick Monitoring Settings
    ENABLE_TICK_MONITORING = True  # Enable detailed tick monitoring
    TICK_MONITORING_INTERVAL = 1   # seconds between tick monitoring updates
    TICK_HISTORY_SIZE = 1000       # Number of ticks to keep in memory
    STRATEGY_INFO_INTERVAL = 1800  # seconds between strategy information logging (30 minutes)

    # Backtest Tick Data Settings
    MAX_TICKS_PER_MINUTE = 1000    # Maximum ticks to process per minute (performance limit)
    TICK_DATA_TIMEOUT = 30         # Timeout in seconds for tick data fetching

    # Trading Hours
    TRADING_START_HOUR = 1
    TRADING_END_HOUR = 15
    TRADING_TIMEZONE = "UTC"

    # Profit Target
    DAILY_PROFIT_TARGET = 90.0
    ENABLE_PROFIT_TARGET = True
    STOP_ON_PROFIT_TARGET = True # Stop trading for the day when target is hit

    # System Settings
    FORCE_CLOSE_EOD = False # Force close all trades at End of Day in backtest
    MAX_OPEN_POSITIONS = 1
    MAGIC_NUMBER = 123456
    ORDER_DEVIATION = 10
    LOG_LEVEL = "INFO"

    # New flags for CLI control
    STRATEGY = "breakout"
    SAVE_RESULTS = False

    ERROR_RECOVERY_DELAY = 5  # Seconds to wait before retrying after an error in live mode

    def __init__(self):
        """Initialize configuration with default values."""
        # Validation is now handled in a separate method
        pass

    def update_from_args(self, args):
        """Update config attributes from parsed command-line arguments."""
        arg_map = {
            'symbol': 'SYMBOL',
            'days': 'BACKTEST_DAYS',
            'strategy': 'STRATEGY',
            'save_results': 'SAVE_RESULTS',
            'timeframe': 'TIMEFRAME',
            'tick_monitoring': 'ENABLE_TICK_MONITORING',
            'tick_stats': 'TICK_STATS_INTERVAL'
        }
        for arg_key, value in vars(args).items():
            if arg_key in ['save_results', 'tick_monitoring'] and value is False:
                # Skip overriding when flag is absent (store_true default False)
                continue
            if arg_key == 'tick_stats' and value:
                # Special handling for tick_stats - enable monitoring and set interval to 10 seconds
                self.ENABLE_TICK_MONITORING = True
                self.TICK_STATS_INTERVAL = 10
                continue
            if value is not None and arg_key in arg_map:
                config_key = arg_map[arg_key]
                if hasattr(self, config_key):
                    # Get the type of the default attribute in Config
                    target_type = type(getattr(self, config_key))
                    # Booleans from argparse (action='store_true') are already bools
                    if target_type == bool and not isinstance(value, bool):
                        setattr(self, config_key, value.lower() in ['true', '1', 't', 'y', 'yes'])
                    else:
                        setattr(self, config_key, target_type(value))
                else:
                    # Allow new attributes from CLI but warn the user
                    logging.warning(f"Unknown config key '{config_key}' supplied via CLI – adding dynamically.")
                    setattr(self, config_key, value)

    def validate(self):
        """Validate core configuration settings."""
        # Core system validations
        if self.PROFIT_LOSS_MULTIPLIER <= 0:
            raise ValueError("PROFIT_LOSS_MULTIPLIER must be a positive number")
        if not isinstance(self.ENABLE_PROFIT_TARGET, bool):
            raise ValueError("ENABLE_PROFIT_TARGET must be a boolean")
        if not isinstance(self.STOP_ON_PROFIT_TARGET, bool):
            raise ValueError("STOP_ON_PROFIT_TARGET must be a boolean")
        if not isinstance(self.FORCE_CLOSE_EOD, bool):
            raise ValueError("FORCE_CLOSE_EOD must be a boolean")
        if self.TRADING_START_HOUR >= self.TRADING_END_HOUR:
            raise ValueError("TRADING_START_HOUR must be less than TRADING_END_HOUR")
        if self.MAX_OPEN_POSITIONS <= 0:
            raise ValueError("MAX_OPEN_POSITIONS must be greater than 0")
        if self.DAILY_PROFIT_TARGET <= 0:
            raise ValueError("DAILY_PROFIT_TARGET must be greater than 0")

        # Timeframe validation
        valid_timeframes = ["M1", "M5", "M15", "M30", "H1", "H4", "D1"]
        if self.TIMEFRAME not in valid_timeframes:
            raise ValueError(f"TIMEFRAME must be one of {valid_timeframes}, got: {self.TIMEFRAME}")

        # Note: Strategy-specific validations are handled by the strategy modules themselves