import logging
import csv
import time
import random
from datetime import datetime, timedelta
from pytz import timezone

from .base_trader import BaseTrader
from utils.visualization import plot_backtest_summary

log = logging.getLogger("ZoneBoS.Backtester")

class Backtester(BaseTrader):
    """Backtesting implementation."""
    
    def __init__(self, config, strategy_module):
        # We need to call super().__init__ to ensure base class attributes are set.
        # However, the base trader might perform actions we don't want during backtest init,
        # so we will call it, but the main orchestration is handled by BaseTrader's run method.
        super().__init__(config, strategy_module)
        self.total_wins = 0
        self.total_losses = 0
        self.total_profit = 0.0
        self.max_losing_streak = 0
        self.all_trades = []
        self._last_processed_price = 0.0
        self._completed_multi_day_trades = []
        self.multi_day_trades = []

    def _save_trade_history(self, trades, filename_prefix="trade_history"):
        """Save trade history to CSV file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{filename_prefix}_{timestamp}.csv"
        try:
            with open(filename, mode='w', newline='') as file:
                writer = csv.DictWriter(file, fieldnames=[
                    "order_ticket", "type", "lot", "entry_price", "exit_price",
                    "sl", "tp", "profit", "status", "entry_time", "close_time",
                    "duration", "martingale_sequence_id", "position_id"
                ])
                writer.writeheader()
                for trade in trades:
                    trade_copy = trade.copy()
                    if trade_copy["duration"] is not None:
                        trade_copy["duration"] = str(trade_copy["duration"])
                    if trade_copy["entry_time"] is not None:
                        trade_copy["entry_time"] = trade_copy["entry_time"].strftime("%Y-%m-%d %H:%M:%S")
                    if trade_copy["close_time"] is not None:
                        trade_copy["close_time"] = trade_copy["close_time"].strftime("%Y-%m-%d %H:%M:%S")
                    writer.writerow(trade_copy)
            log.info(f"Trade history saved to {filename}")
        except Exception as e:
            log.error(f"Error saving trade history: {e}")

    def _process_day_trades(self, df, current_day):
        """Process trades for a single day."""
        # Initialize for this day
        self.daily_profit[current_day.strftime("%Y-%m-%d")] = 0.0
        # self.current_lot = self.cfg.BASE_LOT_SIZE
        # self.mart_steps = 0
        # self.current_martingale_sequence_id = 0

        day_trades = [] # Tracks trades opened on the current day for daily stats
        day_wins, day_losses, day_profit = 0, 0, 0.0
        day_losing_streak = 0
        day_max_losing_streak = 0

        # Initialize strategy for this day using historical data
        # Get current price from the data with safe access
        if df is None or len(df) == 0:
            log.error("Cannot initialize strategy: DataFrame is empty")
            return [], 0, 0, 0.0, 0

        # Safe access to last price with additional validation
        try:
            current_price = df['close'].iloc[-1]
            if current_price <= 0:
                log.error(f"Invalid current price: {current_price}")
                return [], 0, 0, 0.0, 0
        except (IndexError, KeyError) as e:
            log.error(f"Error accessing price data: {e}")
            return [], 0, 0, 0.0, 0

        # Initialize strategy via the dynamically loaded strategy in BaseTrader
        self._initialize_strategy()

        # Initialize previous tick price for entry signal evaluation
        self._prev_tick_price = None

        # Get strategy state for logging
        strategy_state = self.strategy.get_strategy_state()
        strategy_info = strategy_state.get('zones_count', strategy_state.get('strategy_name', 'Unknown'))
        log.info(f"Day {current_day.strftime('%Y-%m-%d')}: Initialized strategy ({strategy_info}) around price {current_price:.2f}.")
        
        # Generate realistic synthetic ticks from OHLC data
        tick_counter = 0
        stop_trading_for_day = False

        for i in range(len(df)):
            if stop_trading_for_day:
                break

            row = df.iloc[i]
            candle_time = df.index[i]

            # Get real tick data for this minute
            ticks = self._get_real_tick_data_for_minute(row, candle_time)

            for tick in ticks:
                tick_counter += 1

                # Track last processed price for multi-day trade reporting
                self._last_processed_price = (tick['ask'] + tick['bid']) / 2

                # 1. First, process any potential trade exits based on the current tick
                day_wins, day_losses, day_profit, day_losing_streak, day_max_losing_streak = self._process_trade_exits(
                    day_trades, tick, tick['time'], current_day, day_wins, day_losses, day_profit, day_losing_streak, day_max_losing_streak
                )

                # 2. Then, check if we should continue trading (hours, profit target)
                tick_time = tick['time']
                should_continue, reason = self._should_continue_trading(tick_time)
                if not should_continue:
                    if reason == "Daily profit target reached":
                        log.info(f"Daily profit target reached at {tick_time.strftime('%H:%M:%S')}, stopping trading for the day")
                        stop_trading_for_day = True
                        break  # Stop processing ticks for the rest of the day
                    elif reason == "Outside trading hours":
                        continue  # Skip this tick but continue processing others

                # 3. Update strategy state
                current_price = (tick['ask'] + tick['bid']) / 2
                self._update_strategy(current_price, tick_time)

                # 4. Finally, evaluate for a new entry signal
                signals = self.evaluate_entry_signals(current_price, tick_time)

                # Process entry signals - Multi-position logic for backtesting
                if signals:
                    for side, signal_data in signals:
                        # Get available position slot
                        position_id = self._get_next_available_position_slot()
                        if position_id is None:
                            log.debug("No available position slots, skipping new entry.")
                            continue

                        # Allocate the position slot
                        if not self._allocate_position_slot(position_id):
                            log.error(f"Failed to allocate position slot {position_id}")
                            continue

                        # Get position size from strategy
                        strategy_lot_size = self.strategy.get_position_size(signal_data, self.trade_history)

                        # Apply martingale if enabled (enhance, don't override)
                        if getattr(self.cfg, 'ENABLE_MARTINGALE', False):
                            _, current_lot, sequence_id = self._get_martingale_step_from_lot(strategy_lot_size, self.trade_history, position_id)
                            
                            # Log martingale state for debugging
                            martingale_state = self.get_martingale_state_for_position(position_id)
                            log.info(f"BACKTEST Position {position_id}: Martingale state - {martingale_state['message']} | "
                                    f"Consecutive losses: {martingale_state['consecutive_losses']} | "
                                    f"Lot size: {current_lot:.2f} (base: {strategy_lot_size:.2f})")
                        else:
                            current_lot = strategy_lot_size
                            sequence_id = self.current_martingale_sequence_id + 1
                            self.current_martingale_sequence_id = sequence_id

                        # Get SL/TP from strategy
                        price = tick['ask'] if side == "Buy" else tick['bid']
                        sl_price = self.strategy.get_stop_loss(side, price, signal_data)
                        tp_price = self.strategy.get_take_profit(side, price, signal_data)

                        # Convert to points for the simulate_order method
                        sl_points = abs(price - sl_price)
                        tp_points = abs(tp_price - price)

                        # Create simulated trade with proper sequence ID
                        trade = self._simulate_order(side, current_lot, sl_points, tp_points, tick, tick['time'], sequence_id, position_id)
                        if trade:
                            log.debug(f"Created trade with position_id: {trade.get('position_id', 'MISSING')}")
                            day_trades.append(trade) # Add to daily report list
                            self.trade_history.append(trade) # Add to persistent history for martingale
                            self.strategy.on_trade_opened(trade)  # Notify strategy
                            log.debug(f"Opened position {position_id}/{self.cfg.MAX_OPEN_POSITIONS} | Open positions: {self.get_open_positions_count(day_trades)}")
                        else:
                            # Release slot if trade creation failed
                            self._release_position_slot(position_id)

        # Force close remaining trades at end of day (if enabled in config)
        if self.cfg.FORCE_CLOSE_EOD:
            day_wins, day_losses, day_profit = self._force_close_remaining_trades(
                day_trades, df, current_day, day_wins, day_losses, day_profit
            )
        else:
            # Report on trades that will span multiple days
            self._report_multi_day_trades(day_trades, current_day)

        return day_trades, day_wins, day_losses, day_profit, day_max_losing_streak

    def _get_real_tick_data_for_minute(self, row, candle_time):
        """Get real tick data for a specific minute from MT5."""
        try:
            # Define the time range for this minute
            start_time = candle_time
            end_time = candle_time + timedelta(minutes=1)

            # Fetch tick data using the broker method
            tick_data = self.broker_get_historical_ticks(
                symbol=self.cfg.SYMBOL,
                start_date=start_time,
                end_date=end_time
            )

            if not tick_data:
                log.warning(f"No real tick data available for {candle_time}, skipping minute")
                return []

            # Apply performance limits
            max_ticks = getattr(self.cfg, 'MAX_TICKS_PER_MINUTE', 1000)
            if len(tick_data) > max_ticks:
                # Sample ticks evenly to stay within limit
                step = len(tick_data) // max_ticks
                tick_data = tick_data[::step][:max_ticks]
                log.debug(f"Limited tick data to {len(tick_data)} ticks for performance")

            log.debug(f"Retrieved {len(tick_data)} real ticks for {candle_time}")
            return tick_data

        except Exception as e:
            log.error(f"Error fetching real tick data for {candle_time}: {e}")
            return []

    def _process_trade_exits(self, day_trades, tick, tick_time, current_day, day_wins, day_losses, day_profit, day_losing_streak, day_max_losing_streak):
        """Process trade exits for the current tick with realistic execution."""
        for trade in day_trades:
            if trade["status"] is None:
                # Validate tick data
                if tick['ask'] <= 0 or tick['bid'] <= 0:
                    continue  # Skip invalid tick

                # For TP: Use bid for Buy trades, ask for Sell trades (realistic execution)
                # For SL: Use bid for Buy trades, ask for Sell trades (realistic execution)
                if trade["type"] == "Buy":
                    tp_price = tick['bid']  # Sell at bid when TP hit
                    sl_price = tick['bid']  # Sell at bid when SL hit
                else:
                    tp_price = tick['ask']  # Buy at ask when TP hit
                    sl_price = tick['ask']  # Buy at ask when SL hit

                # Check for take profit hit
                if (trade["type"] == "Buy" and tp_price >= trade["tp"]) or \
                   (trade["type"] == "Sell" and tp_price <= trade["tp"]):

                    # Calculate profit with realistic execution price using configurable multiplier
                    if trade["type"] == "Buy":
                        profit = (trade["tp"] - trade["entry_price"]) * trade["lot"] * self.cfg.PROFIT_LOSS_MULTIPLIER
                    else:
                        profit = (trade["entry_price"] - trade["tp"]) * trade["lot"] * self.cfg.PROFIT_LOSS_MULTIPLIER

                    # Update trade record
                    trade["profit"] = profit
                    trade["status"] = "Win" if profit > 0 else "Loss"
                    trade["close_time"] = tick_time
                    trade["exit_price"] = trade["tp"]  # Assume TP hit exactly
                    trade["duration"] = tick_time - trade["entry_time"]

                    # Notify strategy of trade closure
                    self.strategy.on_trade_closed(trade)

                    # Update stats
                    day_profit += profit
                    self.daily_profit[current_day.strftime("%Y-%m-%d")] += profit
                    if trade["status"] == "Win":
                        day_wins += 1
                        day_losing_streak = 0
                    else:
                        day_losses += 1
                        day_losing_streak += 1
                    day_max_losing_streak = max(day_max_losing_streak, day_losing_streak)

                    log.info(f"TRADE CLOSED: {trade['type']} | Profit: {profit:.2f} ({trade['status']}) | Duration: {trade['duration']}")

                    # Check if this was a multi-day trade and record its outcome
                    self._record_multi_day_trade_outcome(trade, profit, trade['status'], current_day)

                    # Release position slot when trade closes
                    if trade.get("position_id"):
                        self._release_position_slot(trade["position_id"])
                    else:
                        log.warning(f"Trade closed but no position_id found: {trade}")

                elif (trade["type"] == "Buy" and sl_price <= trade["sl"]) or \
                      (trade["type"] == "Sell" and sl_price >= trade["sl"]):

                    # Calculate loss with realistic execution price using configurable multiplier
                    if trade["type"] == "Buy":
                        loss = (trade["sl"] - trade["entry_price"]) * trade["lot"] * self.cfg.PROFIT_LOSS_MULTIPLIER
                    else:
                        loss = (trade["entry_price"] - trade["sl"]) * trade["lot"] * self.cfg.PROFIT_LOSS_MULTIPLIER

                    # Update trade record
                    trade["profit"] = loss
                    trade["status"] = "Win" if loss > 0 else "Loss"
                    trade["close_time"] = tick_time
                    trade["exit_price"] = trade["sl"]  # Assume SL hit exactly
                    trade["duration"] = tick_time - trade["entry_time"]

                    # Notify strategy of trade closure
                    self.strategy.on_trade_closed(trade)

                    # Update stats
                    day_profit += loss
                    self.daily_profit[current_day.strftime("%Y-%m-%d")] += loss
                    if trade["status"] == "Win":
                        day_wins += 1
                        day_losing_streak = 0
                    else:
                        day_losses += 1
                        day_losing_streak += 1
                    day_max_losing_streak = max(day_max_losing_streak, day_losing_streak)

                    log.info(f"TRADE CLOSED: {trade['type']} | Profit: {loss:.2f} ({trade['status']}) | Duration: {trade['duration']}")

                    # Check if this was a multi-day trade and record its outcome
                    self._record_multi_day_trade_outcome(trade, loss, trade['status'], current_day)

                    # Release position slot when trade closes
                    if trade.get("position_id"):
                        log.debug(f"Releasing position slot {trade['position_id']} for closed trade")
                        self._release_position_slot(trade["position_id"])
                    else:
                        log.warning(f"Trade closed but no position_id found: {trade}")

        return day_wins, day_losses, day_profit, day_losing_streak, day_max_losing_streak

    def _force_close_remaining_trades(self, day_trades, df, current_day, day_wins, day_losses, day_profit):
        """Force close any remaining open trades at the end of the trading day."""
        # Safely get the last price from the day's data
        if df is None or len(df) == 0:
            log.error("Cannot force close trades: DataFrame is empty")
            return day_wins, day_losses, day_profit

        last_row = df.iloc[-1]
        end_of_day_time = df.index[-1].replace(hour=23, minute=59, second=59)

        # Create a final tick for closing trades
        final_tick = {
            'ask': last_row['close'] + 0.0001,  # Small spread
            'bid': last_row['close'] - 0.0001,
            'time': end_of_day_time
        }

        open_trades_count = 0
        for trade in day_trades:
            if trade["status"] is None:
                open_trades_count += 1

                # Force close at current market price
                current_price = last_row['close']

                # Calculate profit/loss at market close using configurable multiplier
                if trade["type"] == "Buy":
                    profit = (current_price - trade["entry_price"]) * trade["lot"] * self.cfg.PROFIT_LOSS_MULTIPLIER
                    exit_price = final_tick['bid']  # Sell at bid
                else:
                    profit = (trade["entry_price"] - current_price) * trade["lot"] * self.cfg.PROFIT_LOSS_MULTIPLIER
                    exit_price = final_tick['ask']  # Buy at ask

                # Update trade record
                trade["profit"] = profit
                trade["status"] = "Win" if profit > 0 else "Loss"
                trade["close_time"] = end_of_day_time
                trade["exit_price"] = exit_price
                trade["duration"] = end_of_day_time - trade["entry_time"]

                # Notify strategy of trade closure
                self.strategy.on_trade_closed(trade)

                # Update stats
                day_profit += profit
                self.daily_profit[current_day.strftime("%Y-%m-%d")] += profit

                if trade["status"] == "Win":
                    day_wins += 1
                else:
                    day_losses += 1

                log.info(f"FORCE CLOSED: {trade['type']} | Profit: {profit:.2f} ({trade['status']}) | End of day close")

                # Record this as a multi-day trade outcome if it was tracked
                self._record_multi_day_trade_outcome(trade, profit, trade['status'], current_day)

                # Release position slot when trade is force closed
                if trade.get("position_id"):
                    log.debug(f"Releasing position slot {trade['position_id']} for force closed trade")
                    self._release_position_slot(trade["position_id"])
                else:
                    log.warning(f"Force closed trade but no position_id found: {trade}")

        if open_trades_count > 0:
            log.info(f"Force closed {open_trades_count} remaining open trades at end of day")

        return day_wins, day_losses, day_profit

    def _report_multi_day_trades(self, day_trades, current_day):
        """Report detailed information about trades that will span multiple days."""
        open_trades = [t for t in day_trades if t.get("status") is None]

        if open_trades:
            log.info(f"=== MULTI-DAY TRADES REPORT - {current_day.strftime('%Y-%m-%d')} ===")
            log.info(f"Found {len(open_trades)} trades that will continue to next trading day:")

            # Add these trades to multi-day tracking
            for trade in open_trades:
                # Create a tracking entry with current status
                multi_day_entry = {
                    'trade': trade.copy(),
                    'start_date': current_day.strftime('%Y-%m-%d'),
                    'reported_date': current_day.strftime('%Y-%m-%d'),
                    'unrealized_pnl': 0.0,
                    'market_price_at_report': getattr(self, '_last_processed_price', 2650.0)
                }
                self.multi_day_trades.append(multi_day_entry)

            for i, trade in enumerate(open_trades, 1):
                # Get the last processed price for accurate P&L calculation
                last_price = getattr(self, '_last_processed_price', 2650.0)

                # Calculate unrealized profit/loss using configurable multiplier
                if trade["type"] == "Buy":
                    unrealized_pnl = (last_price - trade["entry_price"]) * trade["lot"] * self.cfg.PROFIT_LOSS_MULTIPLIER
                else:
                    unrealized_pnl = (trade["entry_price"] - last_price) * trade["lot"] * self.cfg.PROFIT_LOSS_MULTIPLIER

                # Calculate time held so far
                entry_time = trade["entry_time"]
                current_time = current_day.replace(hour=23, minute=59, second=59)
                time_held = current_time - entry_time

                # Format the report
                pnl_status = "PROFIT" if unrealized_pnl > 0 else "LOSS"
                log.info(f"  {i}. Trade Details:")
                log.info(f"     Type: {trade['type']}")
                log.info(f"     Entry Price: {trade['entry_price']:.2f}")
                log.info(f"     Entry Time: {entry_time.strftime('%Y-%m-%d %H:%M:%S')}")
                log.info(f"     Lot Size: {trade['lot']:.2f}")
                log.info(f"     Stop Loss: {trade['sl']:.2f}")
                log.info(f"     Take Profit: {trade['tp']:.2f}")
                log.info(f"     Position ID: {trade.get('position_id', 'N/A')}")
                log.info(f"     Time Held: {time_held}")
                log.info(f"     Current Market Price: {last_price:.2f}")
                log.info(f"     Unrealized P&L: {unrealized_pnl:.2f} ({pnl_status})")

                # Distance to SL/TP
                if trade["type"] == "Buy":
                    sl_distance = last_price - trade["sl"]
                    tp_distance = trade["tp"] - last_price
                else:
                    sl_distance = trade["sl"] - last_price
                    tp_distance = last_price - trade["tp"]

                log.info(f"     Distance to SL: {sl_distance:.2f} ({'Safe' if sl_distance > 0 else 'AT RISK!'})")
                log.info(f"     Distance to TP: {tp_distance:.2f} ({'Close to target' if tp_distance < 10 else 'Far from target'})")
                log.info("")

            log.info(f"These {len(open_trades)} trades will be processed in subsequent trading days.")
            log.info("=" * 60)

    def _record_multi_day_trade_outcome(self, trade, profit, outcome, completion_day):
        """Record the final outcome of a multi-day trade."""
        # Find if this trade was tracked as a multi-day trade
        for i, multi_day_entry in enumerate(self.multi_day_trades):
            if (multi_day_entry['trade']['position_id'] == trade['position_id'] and
                multi_day_entry['trade']['entry_time'] == trade['entry_time']):

                # Move to completed multi-day trades with outcome
                completed_entry = multi_day_entry.copy()
                completed_entry.update({
                    'completion_date': completion_day.strftime('%Y-%m-%d'),
                    'final_profit': profit,
                    'outcome': outcome,
                    'days_held': (completion_day.date() - datetime.strptime(multi_day_entry['start_date'], '%Y-%m-%d').date()).days + 1
                })

                self._completed_multi_day_trades.append(completed_entry)
                # Remove from active multi-day trades
                self.multi_day_trades.pop(i)
                break

    def _report_completed_multi_day_trades(self):
        """Report the final outcomes of completed multi-day trades."""
        if not self._completed_multi_day_trades:
            return

        log.info("\n=== MULTI-DAY TRADES FINAL OUTCOMES ===")
        log.info(f"Summary of {len(self._completed_multi_day_trades)} completed multi-day trades:")

        total_profit = 0
        wins = 0
        losses = 0

        for i, entry in enumerate(self._completed_multi_day_trades, 1):
            trade = entry['trade']
            outcome_symbol = "✅" if entry['outcome'] == "Win" else "❌"

            log.info(f"  {i}. {outcome_symbol} Multi-Day Trade:")
            log.info(f"     Type: {trade['type']}")
            log.info(f"     Entry: {trade['entry_price']:.2f} on {entry['start_date']}")
            log.info(f"     Closed: {entry['completion_date']} ({entry['days_held']} days held)")
            log.info(f"     Final P&L: {entry['final_profit']:.2f} ({entry['outcome']})")
            log.info(f"     Position ID: {trade['position_id']}")
            log.info("")

            total_profit += entry['final_profit']
            if entry['outcome'] == "Win":
                wins += 1
            else:
                losses += 1

        win_rate = (wins / len(self._completed_multi_day_trades)) * 100 if self._completed_multi_day_trades else 0
        log.info(f"Multi-Day Trades Summary:")
        log.info(f"  Total: {len(self._completed_multi_day_trades)}")
        log.info(f"  Wins: {wins} ({win_rate:.1f}%)")
        log.info(f"  Losses: {losses}")
        log.info(f"  Net P&L: {total_profit:.2f}")
        log.info("=" * 50)

    def _report_remaining_multi_day_trades(self):
        """Report any multi-day trades that remained incomplete at backtest end."""
        if not self.multi_day_trades:
            return

        log.info("\n=== INCOMPLETE MULTI-DAY TRADES SUMMARY ===")

        total_unrealized_pnl = 0
        profitable_trades = 0
        losing_trades = 0

        for entry in self.multi_day_trades:
            trade = entry['trade']
            current_price = entry.get('market_price_at_report', 2650.0)

            # Calculate unrealized P&L using actual trade lot size and configurable multiplier
            if trade['type'] == 'Buy':
                unrealized_pnl = (current_price - trade['entry_price']) * trade['lot'] * self.cfg.PROFIT_LOSS_MULTIPLIER
            else:  # Sell
                unrealized_pnl = (trade['entry_price'] - current_price) * trade['lot'] * self.cfg.PROFIT_LOSS_MULTIPLIER

            total_unrealized_pnl += unrealized_pnl

            if unrealized_pnl > 0:
                profitable_trades += 1
            else:
                losing_trades += 1

        log.info(f"Total Incomplete Multi-Day Trades: {len(self.multi_day_trades)}")
        log.info(f"Currently Profitable: {profitable_trades} | Currently Losing: {losing_trades}")
        log.info(f"Total Unrealized P&L: {total_unrealized_pnl:.2f}")
        log.info(f"Note: These trades would continue in live trading or longer backtest")
        log.info("=" * 50)

    def run(self):
        """Main backtesting loop."""
        start_time = time.time()

        # Initialize broker connection for backtesting
        log.info("Initializing broker connection for backtesting...")
        if not self.broker_initialize():
            log.error("Failed to initialize broker connection for backtesting")
            return
        log.info("Broker connection initialized successfully")

        # This will now be called from BaseTrader before this run method
        # self._report_completed_multi_day_trades()

        # Use a fixed end date that's definitely in the past with available data
        # This ensures we have historical data available for backtesting
        end_date = datetime.now(timezone(self.cfg.TRADING_TIMEZONE)) - timedelta(days=7)  # 1 week ago
        start_date = end_date - timedelta(days=self.cfg.BACKTEST_DAYS)

        for i in range(self.cfg.BACKTEST_DAYS):
            current_day = start_date + timedelta(days=i)
            day_str = current_day.strftime("%Y-%m-%d")
            log.info(f"─" * 50)
            log.info(f"Processing backtest for day: {day_str}")

            day_start = current_day.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = current_day.replace(hour=23, minute=59, second=59, microsecond=999999)

            # Fetch data for the current day using the new method in BaseTrader
            df = self.pull_ohlc(
                self.cfg.LOOKBACK_DAYS, self.cfg.BACKTEST_DAYS, 
                start=day_start, end=day_end
            )

            if df is None or df.empty:
                log.warning(f"No data for {day_str}, skipping.")
                continue

            day_trades, day_wins, day_losses, day_profit, day_max_losing_streak = self._process_day_trades(df, current_day)
            
            self.total_wins += day_wins
            self.total_losses += day_losses
            self.total_profit += day_profit
            self.all_trades.extend(day_trades)
            if day_max_losing_streak > self.max_losing_streak:
                self.max_losing_streak = day_max_losing_streak

            log.info(f"Day Summary: Wins: {day_wins}, Losses: {day_losses}, Profit: ${day_profit:.2f}")

        # This will be handled by the BaseTrader after the loop
        # self._report_remaining_multi_day_trades()
        
        if self.cfg.SAVE_RESULTS:
            self._save_trade_history(self.all_trades)
            # Generate visualization using the broker abstraction
            try:
                plot_backtest_summary(
                    self.all_trades,
                    start_date,
                    end_date,
                    self.cfg.SYMBOL,
                    base_trader=self  # Pass self to use broker abstraction
                )
            except Exception as e:
                log.warning(f"Could not generate visualization: {e}")

        elapsed_time = time.time() - start_time
        log.info(f"Backtest finished in {elapsed_time:.2f} seconds.")
        log.info(f"Total Wins: {self.total_wins}, Total Losses: {self.total_losses}, Total Profit: ${self.total_profit:.2f}")
        
        # Calculate final statistics more accurately
        completed_trades = self.total_wins + self.total_losses
        incomplete_trades = len(self.all_trades) - completed_trades

        win_pct = 100 * self.total_wins / completed_trades if completed_trades else 0
        loss_pct = 100 * self.total_losses / completed_trades if completed_trades else 0

        # Calculate profit factor using actual profit amounts (not trade counts)
        total_profit_wins = sum(trade.get("profit", 0) for trade in self.all_trades
                               if trade.get("status") == "Win" and trade.get("profit", 0) > 0)
        total_profit_losses = abs(sum(trade.get("profit", 0) for trade in self.all_trades
                                     if trade.get("status") == "Loss" and trade.get("profit", 0) < 0))

        # Safe profit factor calculation using actual profit amounts
        if total_profit_losses == 0:
            profit_factor = float('inf') if total_profit_wins > 0 else 0.0
        else:
            profit_factor = total_profit_wins / total_profit_losses

        initial_balance = 0  # Starting balance for backtest
        net_profit = self.total_profit - initial_balance

        # Log results
        log.info("\n─── BACKTEST RESULTS ───")
        log.info(f"Force Close EOD: {'ENABLED' if self.cfg.FORCE_CLOSE_EOD else 'DISABLED'}")
        log.info(f"Total Trades: {len(self.all_trades)}")
        log.info(f"Completed Trades: {completed_trades}")
        if incomplete_trades > 0:
            if self.cfg.FORCE_CLOSE_EOD:
                log.info(f"Incomplete Trades: {incomplete_trades} (This should be 0 with FORCE_CLOSE_EOD enabled)")
            else:
                log.info(f"Multi-Day Trades: {incomplete_trades} (Trades spanning multiple days)")
        log.info(f"Wins: {self.total_wins} ({win_pct:.2f}%)")
        log.info(f"Losses: {self.total_losses} ({loss_pct:.2f}%)")
        log.info(f"Profit Factor: {profit_factor:.2f}")
        log.info(f"Initial Balance: {initial_balance:.2f}")
        log.info(f"Final Balance: {self.total_profit:.2f}")
        log.info(f"Net Profit: {net_profit:.2f}")

        log.info("\n─── Daily Performance ───")
        # Daily stats are not directly tracked in the new run method,
        # but we can report the total wins/losses/profit for the entire backtest.
        log.info(f"Total Wins: {self.total_wins}")
        log.info(f"Total Losses: {self.total_losses}")
        log.info(f"Total Profit: ${self.total_profit:.2f}")
        log.info(f"Longest Losing Streak: {self.max_losing_streak}")
        log.info("───────────────────────")

        # Report final outcomes of multi-day trades
        self._report_completed_multi_day_trades()

        # Report any remaining incomplete multi-day trades
        self._report_remaining_multi_day_trades()

        return {
            "total": len(self.all_trades),
            "wins": self.total_wins,
            "losses": self.total_losses,
            "win_pct": win_pct,
            "profit_factor": profit_factor,
            "initial_balance": initial_balance,
            "final_balance": self.total_profit,
            "net_profit": net_profit,
            "daily_stats": [] # No daily stats in the new run method
        }
