import logging
import threading
from datetime import datetime
from typing import List, Tuple, Dict, Any, Optional, Union
from .base_strategy import BaseStrategy

log = logging.getLogger("ZoneBoS.BreakoutStrategy")

class BreakoutStrategy(BaseStrategy):
    """Zone-based breakout and reversal trading strategy.

    This strategy creates fixed-interval zones around a base price and trades
    breakouts through zones and reversals off zones. It includes sophisticated
    zone management with reversal detection, cooldowns, and martingale position sizing.
    
    Features:
    - Multi-confirmation breakout signals with configurable sensitivity
    - Thread-safe confirmation tracking for live trading
    - Comprehensive error handling and input validation
    - Memory-efficient confirmation cleanup
    - Production-ready logging and debugging
    """

    def __init__(self, config):
        super().__init__(config)
        self.zones = []
        self.last_zone_refresh_time = None
        
        # Thread-safe confirmation tracking
        self._confirmation_lock = threading.RLock()
        self.confirmation_tracker = {"confirmations": {}}
        
        # Performance optimization: limit cleanup frequency
        self._last_cleanup_time = None
        self._cleanup_interval = 60  # seconds
        
        # Input validation cache
        self._validated_config = False

        # Track initialization price for first tick processing
        self._initialization_price = None

    def get_strategy_config(self) -> Dict[str, Any]:
        """Return breakout strategy configuration."""
        return {
            # Trade Settings
            "BASE_LOT_SIZE": 0.01,
            "SL_POINTS": 6.2,    # Stop-loss in price points
            "TP_POINTS": 2.35,    # Take-profit in price points (1:1 risk-reward)

            # Martingale Settings
            "ENABLE_MARTINGALE": True,
            "MARTINGALE_MULT": 4.0,
            "MAX_MART_STEPS": 6,
            "MAX_SAFE_LOT_SIZE": 5.0, # Max lot size to prevent excessive risk

            # Multi-Position Settings
            "MAX_OPEN_POSITIONS": 3,  # Allow up to 3 positions simultaneously

            # Zone Settings
            "ZONE_GAP": 2.50,                    # Distance between zones (reduced for more coverage)
            "ZONE_REVERSAL_THRESHOLD": 2.25,    # Price distance to reverse a zone
            "ZONE_CONFIRMATION_CANDLES": 7,     # Number of tick crossings required for confirmation (1 = immediate)
            "NUM_ZONES_PER_SIDE": 400,          # Number of zones on each side of base price
            "ZONE_REFRESH_INTERVAL": 60,        # seconds to update zone classifications
            "ZONE_COOLDOWN_SECONDS": 300,       # 5 minutes cooldown before zone reactivation
            "TRADE_TYPE_COOLDOWN_SECONDS": 1800, # 30 minutes cooldown for same trade type on same zone
            
            # Performance Settings
            "CONFIRMATION_CLEANUP_INTERVAL": 60,  # seconds between confirmation cleanup
            "MAX_CONFIRMATION_ENTRIES": 1000,    # Maximum confirmation entries to prevent memory issues
        }

    def validate_config(self, config_dict: Dict[str, Any]) -> None:
        """Validate breakout strategy configuration with comprehensive checks."""
        try:
            # Zone configuration validation
            if config_dict.get('ZONE_GAP', 1) <= 0:
                raise ValueError("ZONE_GAP must be greater than 0")
            if config_dict.get('ZONE_REFRESH_INTERVAL', 1) <= 0:
                raise ValueError("ZONE_REFRESH_INTERVAL must be a positive number")
            if config_dict.get('MAX_SAFE_LOT_SIZE', 1) <= config_dict.get('BASE_LOT_SIZE', 0.01):
                raise ValueError("MAX_SAFE_LOT_SIZE must be greater than BASE_LOT_SIZE")
            if config_dict.get('NUM_ZONES_PER_SIDE', 1) < 10:
                raise ValueError("NUM_ZONES_PER_SIDE must be at least 10 for safety")

            # Confirmation validation
            zone_confirmation = config_dict.get('ZONE_CONFIRMATION_CANDLES', 1)
            if not isinstance(zone_confirmation, int) or zone_confirmation < 1:
                raise ValueError("ZONE_CONFIRMATION_CANDLES must be a positive integer >= 1")
            if zone_confirmation > 100:
                raise ValueError("ZONE_CONFIRMATION_CANDLES must be <= 100 for practical trading")

            # Performance validation  
            max_confirmations = config_dict.get('MAX_CONFIRMATION_ENTRIES', 1000)
            if not isinstance(max_confirmations, int) or max_confirmations < 100:
                raise ValueError("MAX_CONFIRMATION_ENTRIES must be an integer >= 100")

            # Martingale validation
            if config_dict.get('MARTINGALE_MULT', 1) <= 1:
                raise ValueError("MARTINGALE_MULT must be greater than 1")
            if config_dict.get('MAX_MART_STEPS', 1) < 1:
                raise ValueError("MAX_MART_STEPS must be at least 1")
            
            # Risk management validation
            if config_dict.get('SL_POINTS', 0) <= 0:
                raise ValueError("SL_POINTS must be greater than 0")
            if config_dict.get('TP_POINTS', 0) <= 0:
                raise ValueError("TP_POINTS must be greater than 0")

            self._validated_config = True
            self.logger.info("Breakout strategy configuration validated successfully")
            
        except Exception as e:
            self.logger.error(f"Configuration validation failed: {e}")
            raise

    def initialize(self, current_price: float, historical_data=None) -> None:
        """Initialize the breakout strategy with comprehensive validation."""
        try:
            # Input validation
            if not isinstance(current_price, (int, float)) or current_price <= 0:
                raise ValueError(f"Invalid current_price: {current_price}. Must be a positive number.")
            
            if not self._validated_config:
                raise RuntimeError("Strategy configuration not validated. Call validate_config() first.")

            self.logger.info("Initializing breakout strategy zones...")

            # Dynamically determine a stable base price from the current market price
            # Rounding to the nearest 100 creates a stable but relevant anchor
            base_generation_price = round(current_price / 100) * 100
            self.logger.info(f"Using dynamic base generation price for zones: {base_generation_price}")

            # Create fixed-interval zones from the dynamic base price
            self.zones = self._create_fixed_zones(
                current_price,
                self.config.ZONE_GAP,
                base_generation_price
            )

            # Validate that zones were created successfully
            if not self.zones or len(self.zones) == 0:
                raise RuntimeError("Zone creation failed - no zones were generated")

            # Update zone classifications based on current price
            self.zones = self._update_zone_classification(self.zones, current_price)

            # Initialize confirmation tracker safely
            with self._confirmation_lock:
                self.confirmation_tracker = {"confirmations": {}}

            # Store initialization price for first tick processing
            self._initialization_price = current_price

            self.logger.info(f"Initialized {len(self.zones)} fixed-interval zones from base price {base_generation_price:.2f} around current price {current_price:.2f}")
            self.logger.info(f"Zone gap: {self.config.ZONE_GAP}, Confirmation required: {getattr(self.config, 'ZONE_CONFIRMATION_CANDLES', 1)}")

            # Update refresh time
            if historical_data is not None and not historical_data.empty:
                self.last_zone_refresh_time = historical_data.index[-1]
            else:
                self.last_zone_refresh_time = datetime.now()

        except Exception as e:
            self.logger.error(f"Strategy initialization failed: {e}")
            raise

    def update(self, current_price: float, current_time: Optional[datetime] = None) -> None:
        """Update zone state with current market conditions."""
        try:
            # Input validation
            if not isinstance(current_price, (int, float)) or current_price <= 0:
                self.logger.warning(f"Invalid current_price in update: {current_price}")
                return

            if current_time is None:
                current_time = datetime.now()

            # 1. Check for zone reversals based on the latest price
            self.zones = self._check_zone_reversal(self.zones, current_price, self.config.ZONE_REVERSAL_THRESHOLD)

            # 2. Reactivate zones after cooldown period
            cooldown_seconds = getattr(self.config, 'ZONE_COOLDOWN_SECONDS', 300)
            self.zones = self._reactivate_zones_after_cooldown(self.zones, current_time, cooldown_seconds)

            # 3. Update zone classifications (support/resistance)
            refresh_interval = self.config.ZONE_REFRESH_INTERVAL
            if self.last_zone_refresh_time is None or (current_time.timestamp() - self.last_zone_refresh_time.timestamp()) >= refresh_interval:
                self.zones = self._update_zone_classification(self.zones, current_price)
                self.last_zone_refresh_time = current_time
                self.logger.debug(f"Updated zone classifications for current price: {current_price:.2f}")

        except Exception as e:
            self.logger.error(f"Zone update failed: {e}")
            # Don't raise - allow trading to continue

    def get_entry_signals(self, tick_price: float, prev_tick_price: Optional[float] = None, 
                         current_time: Optional[datetime] = None) -> List[Tuple[str, Dict[str, Any]]]:
        """Generate entry signals with professional error handling and validation.
        
        This method implements a robust zone confirmation system that:
        - Validates all inputs thoroughly
        - Handles edge cases gracefully
        - Provides thread-safe confirmation tracking
        - Includes comprehensive error handling
        - Optimizes performance with intelligent cleanup
        
        Args:
            tick_price: The current tick price
            prev_tick_price: The previous tick price, to detect a cross
            current_time: Current datetime for zone deactivation tracking
            
        Returns:
            List of tuples [(entry_signal, signal_data), ...] for each valid signal
            
        Raises:
            ValueError: If input parameters are invalid
            RuntimeError: If strategy is not properly initialized
        """
        try:
            # Input validation
            if not isinstance(tick_price, (int, float)) or tick_price <= 0:
                self.logger.warning(f"Invalid tick_price: {tick_price}")
                return []

            # Handle first tick after initialization
            if prev_tick_price is None:
                # Use the initialization price as the previous price for the first tick
                # This allows immediate signal generation if price has moved significantly
                if hasattr(self, '_initialization_price') and self._initialization_price is not None:
                    prev_tick_price = self._initialization_price
                    self.logger.debug(f"Using initialization price {prev_tick_price:.2f} as previous price for first tick {tick_price:.2f}")

                    # Check for large price gap after initialization
                    price_gap = abs(tick_price - prev_tick_price)
                    if price_gap > self.config.ZONE_GAP:  # Any significant gap
                        self.logger.info(f"Price gap detected after initialization: {price_gap:.2f} points "
                                       f"(from {prev_tick_price:.2f} to {tick_price:.2f}) - processing signals")
                else:
                    # No initialization price available - skip this tick
                    self.logger.debug(f"First tick after initialization: {tick_price:.2f} - no previous price available")
                    return []

            if not isinstance(prev_tick_price, (int, float)) or prev_tick_price <= 0:
                self.logger.warning(f"Invalid prev_tick_price: {prev_tick_price}")
                return []

            # Check strategy initialization
            if not self.zones:
                self.logger.warning("Strategy not initialized - no zones available")
                return []

            # Initialize confirmation tracker if needed (thread-safe)
            with self._confirmation_lock:
                if self.confirmation_tracker is None:
                    self.confirmation_tracker = {"confirmations": {}}

            # Get configuration parameters with validation
            try:
                confirmation_required = getattr(self.config, 'ZONE_CONFIRMATION_CANDLES', 1)
                if not isinstance(confirmation_required, int) or confirmation_required < 1:
                    self.logger.error(f"Invalid ZONE_CONFIRMATION_CANDLES: {confirmation_required}")
                    confirmation_required = 1

                trade_type_cooldown = getattr(self.config, 'TRADE_TYPE_COOLDOWN_SECONDS', 1800)
                if not isinstance(trade_type_cooldown, (int, float)) or trade_type_cooldown < 0:
                    self.logger.error(f"Invalid TRADE_TYPE_COOLDOWN_SECONDS: {trade_type_cooldown}")
                    trade_type_cooldown = 1800

            except Exception as e:
                self.logger.error(f"Configuration parameter error: {e}")
                return []

            if current_time is None:
                current_time = datetime.now()

            signals = []

            # Debug logging for large price movements
            price_movement = abs(tick_price - prev_tick_price)
            if price_movement > self.config.ZONE_GAP * 2:
                self.logger.info(f"Processing large price movement: {prev_tick_price:.2f} → {tick_price:.2f} "
                               f"({price_movement:.2f} points, >{self.config.ZONE_GAP * 2:.2f} threshold)")

            # Process each zone for potential signals
            zones_crossed = 0
            for zone in self.zones:
                try:
                    # Skip inactive zones
                    if not zone.get('is_active', True):
                        continue

                    # Validate zone data
                    if not self._validate_zone_data(zone):
                        continue

                    zone_price = zone['price']
                    zone_type = zone['zone_type']

                    # Detect price crossings (breakout signals) - IMPROVED FOR FAST PRICE MOVEMENTS
                    # Check if the zone was crossed between prev_tick_price and tick_price
                    crossed_up = False
                    crossed_down = False

                    if prev_tick_price < tick_price:  # Price moving up
                        # Zone was crossed upward if it's between the two prices
                        crossed_up = prev_tick_price < zone_price <= tick_price
                    elif prev_tick_price > tick_price:  # Price moving down
                        # Zone was crossed downward if it's between the two prices
                        crossed_down = prev_tick_price > zone_price >= tick_price

                    # Count crossed zones for debugging
                    if crossed_up or crossed_down:
                        zones_crossed += 1

                    # Determine signal type
                    breakout_signal = None
                    if zone_type == 'resistance' and crossed_up:
                        breakout_signal = "Buy"
                    elif zone_type == 'support' and crossed_down:
                        breakout_signal = "Sell"

                    if breakout_signal:
                        self.logger.debug(f"Zone {zone_price:.2f} ({zone_type}) crossed {breakout_signal} - processing signal")
                        # Process the signal with comprehensive validation
                        signal_result = self._process_breakout_signal(
                            zone, breakout_signal, zone_type, zone_price,
                            confirmation_required, trade_type_cooldown, current_time
                        )

                        if signal_result:
                            signals.append(signal_result)
                            self.logger.debug(f"Signal generated for zone {zone_price:.2f}")

                except Exception as e:
                    self.logger.error(f"Error processing zone {zone.get('price', 'unknown')}: {e}")
                    continue

            # Debug logging for zone crossings
            if price_movement > self.config.ZONE_GAP and zones_crossed == 0:
                self.logger.warning(f"Large price movement ({price_movement:.2f} points) but no zones crossed! "
                                  f"Price: {prev_tick_price:.2f} → {tick_price:.2f}")
                # Additional debugging: check if zones exist in the price range
                zones_in_range = []
                for zone in self.zones:
                    if zone.get('is_active', True):
                        zone_price = zone['price']
                        min_price = min(prev_tick_price, tick_price)
                        max_price = max(prev_tick_price, tick_price)
                        if min_price <= zone_price <= max_price:
                            zones_in_range.append(f"{zone_price:.2f}({zone['zone_type']})")
                if zones_in_range:
                    self.logger.warning(f"Zones in price range but not crossed: {', '.join(zones_in_range)}")
            elif zones_crossed > 0:
                self.logger.debug(f"Price movement crossed {zones_crossed} zones")

            # FAST MOVEMENT ENHANCEMENT: Process any additional zones that might have been crossed
            # This handles cases where price moves so quickly that multiple zones are crossed
            price_movement = abs(tick_price - prev_tick_price)
            if price_movement > self.config.ZONE_GAP:
                additional_signals = self._process_fast_movement_zones(
                    prev_tick_price, tick_price, confirmation_required,
                    current_time, signals
                )
                signals.extend(additional_signals)

                # Log fast movement for debugging
                if price_movement > self.config.ZONE_GAP * 2:
                    self.logger.debug(f"Fast price movement detected: {price_movement:.2f} points "
                                    f"(>{self.config.ZONE_GAP * 2:.2f}), processed {len(additional_signals)} additional zones")

            # Perform cleanup if needed (performance optimization)
            self._cleanup_confirmations(current_time)

            return signals

        except Exception as e:
            self.logger.error(f"Critical error in get_entry_signals: {e}")
            return []

    def _validate_zone_data(self, zone: Dict[str, Any]) -> bool:
        """Validate zone data structure and content."""
        try:
            required_fields = ['price', 'zone_type', 'is_active']
            for field in required_fields:
                if field not in zone:
                    self.logger.warning(f"Zone missing required field: {field}")
                    return False

            if not isinstance(zone['price'], (int, float)) or zone['price'] <= 0:
                self.logger.warning(f"Invalid zone price: {zone['price']}")
                return False

            if zone['zone_type'] not in ['support', 'resistance']:
                self.logger.warning(f"Invalid zone type: {zone['zone_type']}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Zone validation error: {e}")
            return False

    def _process_breakout_signal(self, zone: Dict[str, Any], signal_type: str, zone_type: str, 
                                zone_price: float, confirmation_required: int, 
                                trade_type_cooldown: float, current_time: datetime) -> Optional[Tuple[str, Dict[str, Any]]]:
        """Process breakout signal with comprehensive validation and confirmation tracking."""
        try:
            # Check trade type cooldown
            if not self._is_trade_type_allowed(zone, signal_type, current_time, trade_type_cooldown):
                self.logger.debug(f"Zone at {zone_price:.2f} {signal_type} signal blocked - trade type cooldown active")
                return None

            # Generate zone key for confirmation tracking with unique zone ID
            zone_id = zone.get('zone_id', f"{zone_type}_{zone_price:.2f}")
            zone_key = f"{zone_id}_{signal_type}_breakout"

            # Thread-safe confirmation processing
            with self._confirmation_lock:
                # CONFLICT PREVENTION: Reset confirmations if zone state changed
                if self._has_zone_state_changed(zone, zone_key):
                    self._reset_zone_confirmations(zone, zone_key)

                if confirmation_required <= 1:
                    # Immediate signal - no confirmation needed
                    return self._generate_immediate_signal(zone, signal_type, zone_type, current_time)
                else:
                    # Multi-confirmation signal processing
                    return self._process_multi_confirmation_signal(
                        zone, signal_type, zone_type, zone_price, zone_key,
                        confirmation_required, current_time
                    )

        except Exception as e:
            self.logger.error(f"Error processing breakout signal: {e}")
            return None

    def _has_zone_state_changed(self, zone: Dict[str, Any], zone_key: str) -> bool:
        """Check if zone state has changed since last confirmation."""
        try:
            # Get stored zone state for this confirmation key
            stored_state = self.confirmation_tracker.get("zone_states", {}).get(zone_key, {})
            
            current_state = {
                'is_active': zone.get('is_active', True),
                'zone_type': zone.get('zone_type'),
                'is_reversed': zone.get('is_reversed', False),
                'last_break_time': zone.get('last_break_time'),
                'break_count': zone.get('break_count', 0)
            }
            
            # Check if state has changed
            for key, value in current_state.items():
                if stored_state.get(key) != value:
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking zone state change: {e}")
            return True  # Reset on error to be safe

    def _reset_zone_confirmations(self, zone: Dict[str, Any], zone_key: str) -> None:
        """Reset confirmations for a zone when its state changes."""
        try:
            # Reset confirmation count
            self.confirmation_tracker["confirmations"][zone_key] = 0
            
            # Update stored zone state
            if "zone_states" not in self.confirmation_tracker:
                self.confirmation_tracker["zone_states"] = {}
            
            self.confirmation_tracker["zone_states"][zone_key] = {
                'is_active': zone.get('is_active', True),
                'zone_type': zone.get('zone_type'),
                'is_reversed': zone.get('is_reversed', False),
                'last_break_time': zone.get('last_break_time'),
                'break_count': zone.get('break_count', 0)
            }
            
            self.logger.debug(f"Reset confirmations for zone key {zone_key} due to state change")
            
        except Exception as e:
            self.logger.error(f"Error resetting zone confirmations: {e}")

    def _generate_immediate_signal(self, zone: Dict[str, Any], signal_type: str, 
                                  zone_type: str, current_time: datetime) -> Tuple[str, Dict[str, Any]]:
        """Generate immediate signal and deactivate zone."""
        try:
            # Create signal data
            signal_data = {
                "type": zone_type,
                "zone": zone.copy(),  # Use copy to avoid reference issues
                "signal_type": "breakout",
                "confirmations": 1,
                "timestamp": current_time
            }

            # Update trade type timestamp
            self._update_trade_type_timestamp(zone, signal_type, current_time)

            # CONFLICT PREVENTION: Clear any pending confirmations for this zone
            self._clear_zone_confirmations(zone, signal_type)

            # Deactivate zone
            zone['is_active'] = False
            zone['deactivated_time'] = current_time

            self.logger.info(f"Zone at {zone['price']:.2f} DEACTIVATED after breakout signal for {signal_type} (immediate)")

            return (signal_type, signal_data)

        except Exception as e:
            self.logger.error(f"Error generating immediate signal: {e}")
            raise

    def _clear_zone_confirmations(self, zone: Dict[str, Any], signal_type: str) -> None:
        """Clear all confirmations for a zone when it's deactivated."""
        try:
            zone_id = zone.get('zone_id', f"{zone.get('zone_type')}_{zone['price']:.2f}")
            zone_key = f"{zone_id}_{signal_type}_breakout"
            
            # Clear confirmation count
            if zone_key in self.confirmation_tracker["confirmations"]:
                del self.confirmation_tracker["confirmations"][zone_key]
            
            # Clear stored zone state
            if "zone_states" in self.confirmation_tracker and zone_key in self.confirmation_tracker["zone_states"]:
                del self.confirmation_tracker["zone_states"][zone_key]
            
            self.logger.debug(f"Cleared confirmations for zone key {zone_key}")
            
        except Exception as e:
            self.logger.error(f"Error clearing zone confirmations: {e}")

    def _clear_zone_confirmations_on_reactivation(self, zone: Dict[str, Any]) -> None:
        """Clear all confirmations for a zone when it's reactivated."""
        try:
            zone_id = zone.get('zone_id', f"{zone.get('zone_type')}_{zone['price']:.2f}")
            
            # Clear confirmations for both Buy and Sell signals
            for signal_type in ['Buy', 'Sell']:
                zone_key = f"{zone_id}_{signal_type}_breakout"
                
                # Clear confirmation count
                if zone_key in self.confirmation_tracker["confirmations"]:
                    del self.confirmation_tracker["confirmations"][zone_key]
                
                # Clear stored zone state
                if "zone_states" in self.confirmation_tracker and zone_key in self.confirmation_tracker["zone_states"]:
                    del self.confirmation_tracker["zone_states"][zone_key]
            
            self.logger.debug(f"Cleared all confirmations for zone {zone_id} on reactivation")
            
        except Exception as e:
            self.logger.error(f"Error clearing zone confirmations on reactivation: {e}")

    def _process_multi_confirmation_signal(self, zone: Dict[str, Any], signal_type: str,
                                          zone_type: str, zone_price: float, zone_key: str,
                                          confirmation_required: int, current_time: datetime) -> Optional[Tuple[str, Dict[str, Any]]]:
        """Process multi-confirmation signal with momentum-based entry logic.

        NEW STRATEGY: When multiple zones are being crossed quickly:
        1. Enter trades with partial confirmations to catch momentum
        2. Reset confirmation countdown for subsequent zones
        3. Prioritize speed over waiting for full confirmations
        """
        try:
            # Update zone state tracking
            self._update_zone_state_tracking(zone, zone_key)

            # Get current confirmation count and increment
            current_confirmations = self.confirmation_tracker["confirmations"].get(zone_key, 0) + 1
            self.confirmation_tracker["confirmations"][zone_key] = current_confirmations

            self.logger.debug(f"Zone at {zone_price:.2f} {signal_type} confirmation {current_confirmations}/{confirmation_required}")

            # MOMENTUM ENTRY LOGIC: Check if we should enter with partial confirmations
            should_enter = False
            entry_reason = "standard"

            # Standard entry: Full confirmations reached
            if current_confirmations >= confirmation_required:
                should_enter = True
                entry_reason = "full_confirmations"

            # MOMENTUM ENTRY: Check if other zones are accumulating confirmations (indicating fast movement)
            elif current_confirmations >= max(1, confirmation_required // 3):  # At least 1/3 of required confirmations
                # Count how many other zones have partial confirmations (indicating momentum)
                momentum_zones = 0
                with self._confirmation_lock:
                    for key, count in self.confirmation_tracker["confirmations"].items():
                        if key != zone_key and count > 0:
                            momentum_zones += 1

                # If multiple zones are accumulating confirmations, enter on momentum
                if momentum_zones >= 2:  # At least 2 other zones have confirmations
                    should_enter = True
                    entry_reason = "momentum_entry"
                    self.logger.info(f"MOMENTUM ENTRY triggered: Zone {zone_price:.2f} with {current_confirmations}/{confirmation_required} "
                                   f"confirmations ({momentum_zones} other zones active)")

            if should_enter:
                # Generate signal and deactivate zone
                signal_data = {
                    "type": zone_type,
                    "zone": zone.copy(),
                    "signal_type": "breakout",
                    "confirmations": current_confirmations,
                    "timestamp": current_time,
                    "entry_reason": entry_reason,
                    "momentum_entry": entry_reason == "momentum_entry"
                }

                # Update trade type timestamp
                self._update_trade_type_timestamp(zone, signal_type, current_time)

                # RESET STRATEGY: Clear confirmations for this zone and start fresh countdown for others
                self._clear_zone_confirmations(zone, signal_type)

                # Deactivate zone
                zone['is_active'] = False
                zone['deactivated_time'] = current_time

                log_message = f"Zone at {zone_price:.2f} DEACTIVATED after {entry_reason} signal for {signal_type} with {current_confirmations} confirmations"
                self.logger.info(log_message)

                return (signal_type, signal_data)

            # Not enough confirmations yet - continue accumulating
            return None

        except Exception as e:
            self.logger.error(f"Error processing multi-confirmation signal: {e}")
            return None

    def _update_zone_state_tracking(self, zone: Dict[str, Any], zone_key: str) -> None:
        """Update zone state tracking for confirmation consistency."""
        try:
            if "zone_states" not in self.confirmation_tracker:
                self.confirmation_tracker["zone_states"] = {}
            
            self.confirmation_tracker["zone_states"][zone_key] = {
                'is_active': zone.get('is_active', True),
                'zone_type': zone.get('zone_type'),
                'is_reversed': zone.get('is_reversed', False),
                'last_break_time': zone.get('last_break_time'),
                'break_count': zone.get('break_count', 0)
            }
            
        except Exception as e:
            self.logger.error(f"Error updating zone state tracking: {e}")

    def _process_fast_movement_zones(self, prev_price: float, current_price: float,
                                   confirmation_required: int, current_time: datetime,
                                   existing_signals: List) -> List[Tuple[str, Dict[str, Any]]]:
        """Process zones that were crossed during fast price movements.

        NEW BEHAVIOR: When price crosses multiple zones quickly:
        1. Enter trades immediately for zones with any confirmations
        2. Start fresh confirmation countdown for subsequent zones
        3. Prioritize momentum over waiting for full confirmations
        """
        additional_signals = []

        try:
            # Get the price range that was crossed
            min_price = min(prev_price, current_price)
            max_price = max(prev_price, current_price)
            price_direction = 1 if current_price > prev_price else -1

            # Find all zones that were crossed during the movement
            crossed_zones = []
            for zone in self.zones:
                if not zone.get('is_active', True):
                    continue

                zone_price = zone['price']

                # Check if this zone was crossed during the movement
                if min_price < zone_price < max_price:
                    crossed_zones.append(zone)

            # Sort zones by price to process them in order of crossing
            if price_direction > 0:  # Price moving up
                crossed_zones.sort(key=lambda z: z['price'])
            else:  # Price moving down
                crossed_zones.sort(key=lambda z: z['price'], reverse=True)

            # FAST MOVEMENT STRATEGY: Process zones with momentum-based entry
            for i, zone in enumerate(crossed_zones):
                try:
                    zone_price = zone['price']
                    zone_type = zone['zone_type']

                    # Determine signal type
                    should_signal = False
                    signal_type = None

                    if price_direction > 0 and zone_type == 'resistance':
                        should_signal = True
                        signal_type = "Buy"
                    elif price_direction < 0 and zone_type == 'support':
                        should_signal = True
                        signal_type = "Sell"

                    if should_signal and signal_type:
                        # Check if we already processed this zone in the main loop
                        zone_id = zone.get('zone_id', f"{zone_type}_{zone_price:.2f}")
                        already_processed = any(
                            sig[1].get('zone', {}).get('zone_id') == zone_id
                            for sig in existing_signals
                        )

                        if not already_processed:
                            # MOMENTUM ENTRY: For fast movements, enter with reduced confirmation requirement
                            zone_key = f"{zone_id}_{signal_type}_breakout"

                            with self._confirmation_lock:
                                current_confirmations = self.confirmation_tracker["confirmations"].get(zone_key, 0)

                                # FAST MOVEMENT LOGIC:
                                # - First zone crossed: Enter immediately if any confirmations exist
                                # - Subsequent zones: Enter with reduced confirmation requirement
                                fast_movement_threshold = max(1, confirmation_required // 2) if i == 0 else 1

                                if current_confirmations >= fast_movement_threshold:
                                    # Generate immediate signal for momentum entry
                                    signal_data = {
                                        "type": zone_type,
                                        "zone": zone.copy(),
                                        "signal_type": "breakout",
                                        "confirmations": current_confirmations,
                                        "timestamp": current_time,
                                        "fast_movement": True,
                                        "momentum_entry": True,
                                        "zone_sequence": i + 1,
                                        "total_zones_crossed": len(crossed_zones)
                                    }

                                    # Update trade type timestamp
                                    self._update_trade_type_timestamp(zone, signal_type, current_time)

                                    # Clear confirmations and deactivate zone
                                    self._clear_zone_confirmations(zone, signal_type)
                                    zone['is_active'] = False
                                    zone['deactivated_time'] = current_time

                                    additional_signals.append((signal_type, signal_data))

                                    self.logger.info(f"MOMENTUM ENTRY: Zone {zone_price:.2f} {signal_type} "
                                                   f"(confirmations: {current_confirmations}/{confirmation_required}, "
                                                   f"sequence: {i+1}/{len(crossed_zones)})")
                                else:
                                    # Add confirmation for this zone and continue countdown
                                    self.confirmation_tracker["confirmations"][zone_key] = current_confirmations + 1
                                    self.logger.debug(f"Fast movement: Zone {zone_price:.2f} confirmation "
                                                    f"{current_confirmations + 1}/{confirmation_required}")

                except Exception as e:
                    self.logger.error(f"Error processing fast movement zone {zone.get('price', 'unknown')}: {e}")
                    continue

            if additional_signals:
                self.logger.info(f"Fast movement: Generated {len(additional_signals)} momentum entries "
                               f"from {len(crossed_zones)} zones crossed")

        except Exception as e:
            self.logger.error(f"Error in _process_fast_movement_zones: {e}")

        return additional_signals

    def _cleanup_confirmations(self, current_time: datetime) -> None:
        """Clean up stale confirmations with performance optimization."""
        try:
            # Performance optimization: only cleanup periodically
            cleanup_interval = getattr(self.config, 'CONFIRMATION_CLEANUP_INTERVAL', 60)
            if (self._last_cleanup_time is not None and 
                (current_time - self._last_cleanup_time).total_seconds() < cleanup_interval):
                return

            with self._confirmation_lock:
                # Generate active keys for cleanup
                active_keys = set()
                for zone in self.zones:
                    if zone.get('is_active', True) and self._validate_zone_data(zone):
                        zone_id = zone.get('zone_id', f"{zone['zone_type']}_{zone['price']:.2f}")
                        # Include both Buy and Sell keys for each zone
                        active_keys.add(f"{zone_id}_Buy_breakout")
                        active_keys.add(f"{zone_id}_Sell_breakout")

                # CONFLICT PREVENTION: Remove stale confirmations and zone states
                stale_keys = [k for k in self.confirmation_tracker["confirmations"] if k not in active_keys]
                for key in stale_keys:
                    del self.confirmation_tracker["confirmations"][key]
                
                # Clean up zone states
                if "zone_states" in self.confirmation_tracker:
                    stale_state_keys = [k for k in self.confirmation_tracker["zone_states"] if k not in active_keys]
                    for key in stale_state_keys:
                        del self.confirmation_tracker["zone_states"][key]

                # Memory management: prevent excessive confirmation entries
                max_entries = getattr(self.config, 'MAX_CONFIRMATION_ENTRIES', 1000)
                if len(self.confirmation_tracker["confirmations"]) > max_entries:
                    # Keep only the most recent entries
                    sorted_keys = sorted(self.confirmation_tracker["confirmations"].keys())
                    keys_to_remove = sorted_keys[:-max_entries]
                    for key in keys_to_remove:
                        del self.confirmation_tracker["confirmations"][key]
                        # Also remove corresponding zone state
                        if "zone_states" in self.confirmation_tracker and key in self.confirmation_tracker["zone_states"]:
                            del self.confirmation_tracker["zone_states"][key]
                    
                    self.logger.warning(f"Cleaned up {len(keys_to_remove)} confirmation entries to prevent memory issues")

                if stale_keys:
                    self.logger.debug(f"Cleaned up {len(stale_keys)} stale confirmation entries")

            self._last_cleanup_time = current_time

        except Exception as e:
            self.logger.error(f"Error during confirmation cleanup: {e}")

    # --- Zone Management Methods ---

    def _create_fixed_zones(self, current_price, zone_gap, base_generation_price):
        """Create a large but finite number of fixed-interval zones around a base price.

        This ensures that zones remain consistent across different trading sessions
        and are not regenerated based on fluctuating current price.

        Args:
            current_price: Current market price (used for initial classification)
            zone_gap: Distance between zones
            base_generation_price: The stable price from which zones are generated

        Returns:
            List of zones with support/resistance classification
        """
        zones = []

        # Generate a large, but finite, number of zones for practical purposes
        num_zones_per_side = getattr(self.config, 'NUM_ZONES_PER_SIDE', 100)

        # Generate zones from the base price to ensure consistency
        # Create zones both below and above the current price for proper support/resistance coverage
        for i in range(-num_zones_per_side, num_zones_per_side + 1):
            zone_price = base_generation_price + (i * zone_gap)

            # Only create zones with positive prices for trading
            if zone_price <= 0:
                continue

            zone = {
                'price': zone_price,
                'zone_type': 'support' if zone_price < current_price else 'resistance',
                'is_reversed': False,
                'last_break_time': None,
                'break_count': 0,
                'is_active': True,  # A zone is active and can be traded
                'deactivated_time': None,  # Track when zone was deactivated for cooldown
                'last_buy_trade_time': None,   # Track last buy trade time for cooldown
                'last_sell_trade_time': None,   # Track last sell trade time for cooldown
                'zone_id': f"zone_{zone_price:.2f}_{i}_{id(self)}"  # Unique zone ID for confirmation tracking
            }
            zones.append(zone)

        # Ensure we have zones above current price for resistance - extend if needed
        max_zone_price = max(zone['price'] for zone in zones) if zones else base_generation_price
        if max_zone_price <= current_price:
            # Add additional zones above current price for resistance
            additional_zones_needed = max(10, num_zones_per_side // 2)  # At least 10 zones above
            for i in range(1, additional_zones_needed + 1):
                zone_price = current_price + (i * zone_gap)
                zone = {
                    'price': zone_price,
                    'zone_type': 'resistance',
                    'is_reversed': False,
                    'last_break_time': None,
                    'break_count': 0,
                    'is_active': True,
                    'deactivated_time': None,
                    'last_buy_trade_time': None,
                    'last_sell_trade_time': None
                }
                zones.append(zone)

        zones.sort(key=lambda z: z['price'])
        self.logger.info(f"Created {len(zones)} fixed-interval zones from base price {base_generation_price:.2f}")
        return zones

    def _update_zone_classification(self, zones, current_price):
        """Update zone classification based on current price, respecting reversals.

        Args:
            zones: List of zones to update
            current_price: Current market price

        Returns:
            Updated zones with correct support/resistance classification
        """
        for zone in zones:
            # If a zone is reversed, its type is fixed until reversed back
            if zone.get('is_reversed', False):
                continue

            # Classify based on price relative to the zone
            if zone['price'] > current_price:
                zone['zone_type'] = 'resistance'
            else:
                zone['zone_type'] = 'support'
        return zones

    def _check_zone_reversal(self, zones, current_price, reversal_threshold):
        """Check if zones should be reversed or restored based on price movement.

        - A resistance zone becomes support if the price moves significantly above it.
        - A support zone becomes resistance if the price moves significantly below it.
        - A reversed zone can be restored to its original type if the price
          moves back across the original zone price.

        Args:
            zones: List of zones to check
            current_price: Current market price
            reversal_threshold: Distance price must move beyond a zone for reversal.

        Returns:
            Updated zones with reversal status.
        """
        for zone in zones:
            zone_price = zone['price']
            is_reversed = zone.get('is_reversed', False)

            if not is_reversed:
                # Logic to reverse a normal zone
                if zone['zone_type'] == 'resistance' and current_price > zone_price + reversal_threshold:
                    # Resistance flips to Support
                    zone['zone_type'] = 'support'
                    zone['is_reversed'] = True
                    zone['last_break_time'] = datetime.now()
                    zone['break_count'] += 1
                    zone['is_active'] = True # Reactivate the zone for new trades
                    
                    # CONFLICT PREVENTION: Clear confirmations when zone is reversed
                    with self._confirmation_lock:
                        self._clear_zone_confirmations_on_reactivation(zone)
                    
                    self.logger.info(f"Resistance at {zone_price:.2f} REVERSED to Support at {datetime.now()}. Zone REACTIVATED.")

                elif zone['zone_type'] == 'support' and current_price < zone_price - reversal_threshold:
                    # Support flips to Resistance
                    zone['zone_type'] = 'resistance'
                    zone['is_reversed'] = True
                    zone['last_break_time'] = datetime.now()
                    zone['break_count'] += 1
                    zone['is_active'] = True # Reactivate the zone for new trades
                    
                    # CONFLICT PREVENTION: Clear confirmations when zone is reversed
                    with self._confirmation_lock:
                        self._clear_zone_confirmations_on_reactivation(zone)
                    
                    self.logger.info(f"Support at {zone_price:.2f} REVERSED to Resistance at {datetime.now()}. Zone REACTIVATED.")

            else:
                # Logic to restore a reversed zone once price has moved
                # a full reversal_threshold in the opposite direction to avoid
                # immediate restoration that blocks breakout trades.

                if zone['zone_type'] == 'support' and current_price < zone_price - reversal_threshold:
                    # A reversed resistance (now support) restores to resistance
                    zone['zone_type'] = 'resistance'
                    zone['is_reversed'] = False
                    zone['is_active'] = True  # Reactivate the zone
                    
                    # CONFLICT PREVENTION: Clear confirmations when zone is restored
                    with self._confirmation_lock:
                        self._clear_zone_confirmations_on_reactivation(zone)
                    
                    self.logger.info(
                        f"Reversed zone at {zone_price:.2f} RESTORED to Resistance at {datetime.now()} "
                        f"after price moved {reversal_threshold:.2f} below the zone. Zone REACTIVATED."
                    )

                elif zone['zone_type'] == 'resistance' and current_price > zone_price + reversal_threshold:
                    # A reversed support (now resistance) restores to support
                    zone['zone_type'] = 'support'
                    zone['is_reversed'] = False
                    zone['is_active'] = True  # Reactivate the zone
                    
                    # CONFLICT PREVENTION: Clear confirmations when zone is restored
                    with self._confirmation_lock:
                        self._clear_zone_confirmations_on_reactivation(zone)
                    
                    self.logger.info(
                        f"Reversed zone at {zone_price:.2f} RESTORED to Support at {datetime.now()} "
                        f"after price moved {reversal_threshold:.2f} above the zone. Zone REACTIVATED."
                    )

        return zones

    def _reactivate_zones_after_cooldown(self, zones, current_time, cooldown_seconds):
        """Reactivate zones after cooldown period expires.

        Args:
            zones: List of zones to check
            current_time: Current datetime
            cooldown_seconds: Cooldown period in seconds

        Returns:
            Updated zones with reactivated zones
        """
        for zone in zones:
            if not zone.get('is_active', True) and zone.get('deactivated_time'):
                time_since_deactivation = (current_time - zone['deactivated_time']).total_seconds()
                if time_since_deactivation >= cooldown_seconds:
                    zone['is_active'] = True
                    zone['deactivated_time'] = None
                    
                    # CONFLICT PREVENTION: Clear confirmations when zone is reactivated
                    with self._confirmation_lock:
                        self._clear_zone_confirmations_on_reactivation(zone)
                    
                    self.logger.info(f"Zone at {zone['price']:.2f} REACTIVATED after {cooldown_seconds}s cooldown.")

        return zones

    def _is_trade_type_allowed(self, zone, trade_type, current_time, cooldown_seconds):
        """Check if a trade type is allowed on a zone based on cooldown period.

        Args:
            zone: Zone dictionary
            trade_type: 'Buy' or 'Sell'
            current_time: Current datetime
            cooldown_seconds: Cooldown period in seconds

        Returns:
            bool: True if trade type is allowed, False if still in cooldown
        """
        if trade_type == "Buy":
            last_trade_time = zone.get('last_buy_trade_time')
        else:  # Sell
            last_trade_time = zone.get('last_sell_trade_time')

        if last_trade_time is None:
            return True  # No previous trade of this type

        time_since_last_trade = (current_time - last_trade_time).total_seconds()
        return time_since_last_trade >= cooldown_seconds

    def _update_trade_type_timestamp(self, zone, trade_type, current_time):
        """Update the timestamp for the last trade of a given type on a zone.

        Args:
            zone: Zone dictionary to update
            trade_type: 'Buy' or 'Sell'
            current_time: Current datetime
        """
        if trade_type == "Buy":
            zone['last_buy_trade_time'] = current_time
        else:  # Sell
            zone['last_sell_trade_time'] = current_time

    def on_trade_closed(self, trade_data: Dict[str, Any]) -> None:
        """Handle trade closure events with strategy information display."""
        try:
            # Get current price from trade data
            current_price = trade_data.get('exit_price', 0)
            if current_price <= 0:
                current_price = trade_data.get('entry_price', 0)
                
            # Get open positions count
            open_positions_count = trade_data.get('open_positions_count', 0)
            
            # Display strategy information
            if current_price > 0:
                nearest_support, nearest_resistance = self._get_nearest_zones(current_price)
                support_str = f"Support: {nearest_support:.2f}" if nearest_support else "Support: N/A"
                resistance_str = f"Resistance: {nearest_resistance:.2f}" if nearest_resistance else "Resistance: N/A"
                active_zones = len([z for z in self.zones if z.get('is_active', True)])
                
                self.logger.info(f"🎯 BREAKOUT STRATEGY | Price: {current_price:.2f} | "
                               f"{support_str} | {resistance_str} | "
                               f"Zones: {active_zones}/{len(self.zones)} active")
            
            # Display open positions count
            self.logger.info(f"Open positions: {open_positions_count}")
            
        except Exception as e:
            self.logger.error(f"Error in on_trade_closed: {e}")

    def _get_nearest_zones(self, current_price):
        """Get the nearest support and resistance zones to current price."""
        try:
            nearest_support = None
            nearest_resistance = None
            
            for zone in self.zones:
                if not zone.get('is_active', True):
                    continue
                    
                zone_price = zone['price']
                zone_type = zone['zone_type']
                
                if zone_type == 'support' and zone_price < current_price:
                    if nearest_support is None or zone_price > nearest_support:
                        nearest_support = zone_price
                elif zone_type == 'resistance' and zone_price > current_price:
                    if nearest_resistance is None or zone_price < nearest_resistance:
                        nearest_resistance = zone_price
            
            return nearest_support, nearest_resistance
            
        except Exception as e:
            self.logger.error(f"Error getting nearest zones: {e}")
            return None, None

    def get_display_info(self, current_price: float) -> str:
        """Get strategy display information for live trading output."""
        try:
            nearest_support, nearest_resistance = self._get_nearest_zones(current_price)
            support_str = f"Support: {nearest_support:.2f}" if nearest_support else "Support: N/A"
            resistance_str = f"Resistance: {nearest_resistance:.2f}" if nearest_resistance else "Resistance: N/A"
            active_zones = len([z for z in self.zones if z.get('is_active', True)])
            
            return (f"🎯 BREAKOUT STRATEGY | Price: {current_price:.2f} | "
                   f"{support_str} | {resistance_str} | "
                   f"Zones: {active_zones}/{len(self.zones)} active")
        except Exception as e:
            self.logger.error(f"Error in get_display_info: {e}")
            return f"🎯 BREAKOUT STRATEGY | Price: {current_price:.2f} | Status: Error"

    def get_signal_description(self, side: str, signal_data: Dict[str, Any], current_price: float) -> str:
        """Get custom signal description for logging."""
        try:
            signal_type = signal_data.get('signal_type', 'unknown')
            zone_info = signal_data.get('zone', {})
            zone_price = zone_info.get('price', 0)
            zone_type = signal_data.get('type', 'unknown')
            confirmations = signal_data.get('confirmations', 1)
            
            if signal_type == 'breakout':
                return (f"🚀 BREAKOUT SIGNAL | {side.upper()} | Price: {current_price:.2f} | "
                       f"Zone: {zone_price:.2f} ({zone_type}) | Confirmations: {confirmations}")
            else:
                return f"📊 BREAKOUT SIGNAL | {side.upper()} | Price: {current_price:.2f} | Type: {signal_type}"
                
        except Exception as e:
            self.logger.error(f"Error in get_signal_description: {e}")
            return f"📊 BREAKOUT SIGNAL | {side.upper()} | Price: {current_price:.2f}"

    def get_strategy_state(self) -> Dict[str, Any]:
        """Get current strategy state for monitoring and debugging."""
        try:
            with self._confirmation_lock:
                active_zones = len([z for z in self.zones if z.get('is_active', True)])
                active_confirmations = len(self.confirmation_tracker["confirmations"])
                
                return {
                    'strategy_name': 'BreakoutStrategy',
                    'zones_count': len(self.zones),
                    'active_zones': active_zones,
                    'active_confirmations': active_confirmations,
                    'last_refresh': self.last_zone_refresh_time,
                    'validated_config': self._validated_config
                }
        except Exception as e:
            self.logger.error(f"Error getting strategy state: {e}")
            return {'strategy_name': 'BreakoutStrategy', 'error': str(e)}


# --- Backward Compatibility Functions ---
# These functions maintain compatibility with the old function-based interface

def get_strategy_instance(config):
    """Factory function to create a BreakoutStrategy instance."""
    return BreakoutStrategy(config)

# Legacy function exports for backward compatibility
def create_strategy_config():
    """Get strategy configuration for backward compatibility."""
    temp_config = type('Config', (), {})()  # Create empty config object
    temp_strategy = BreakoutStrategy(temp_config)
    return temp_strategy.get_strategy_config()

strategy_config = create_strategy_config()

def validate_strategy_config(config_dict):
    """Legacy validation function."""
    temp_config = type('Config', (), {})()
    temp_strategy = BreakoutStrategy(temp_config)
    temp_strategy.validate_config(config_dict)