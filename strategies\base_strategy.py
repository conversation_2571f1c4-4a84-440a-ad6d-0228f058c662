from abc import ABC, abstractmethod
import logging
from datetime import datetime
from typing import List, Tuple, Dict, Any, Optional

log = logging.getLogger("ZoneBoS.BaseStrategy")

class BaseStrategy(ABC):
    """Abstract base class for all trading strategies.
    
    This class defines the interface that all strategies must implement.
    It provides a clean separation between strategy logic and the core
    trading infrastructure (data gathering, order execution, backtesting).
    
    Strategies can implement any trading logic they want - zones, indicators,
    patterns, machine learning models, etc. The BaseTrader will handle all
    the infrastructure concerns.
    """
    
    def __init__(self, config):
        """Initialize the strategy with configuration.
        
        Args:
            config: Configuration object containing strategy parameters
        """
        self.config = config
        self.logger = logging.getLogger(f"ZoneBoS.{self.__class__.__name__}")
        
    @abstractmethod
    def get_strategy_config(self) -> Dict[str, Any]:
        """Return strategy-specific configuration parameters.
        
        This allows each strategy to define its own configuration parameters
        that will be merged with the base configuration.
        
        Returns:
            Dictionary of configuration key-value pairs
        """
        pass
    
    @abstractmethod
    def validate_config(self, config_dict: Dict[str, Any]) -> None:
        """Validate strategy-specific configuration.
        
        Args:
            config_dict: Dictionary of configuration parameters to validate
            
        Raises:
            ValueError: If configuration is invalid
        """
        pass
    
    @abstractmethod
    def initialize(self, current_price: float, historical_data=None) -> None:
        """Initialize the strategy state.
        
        This is called once when the strategy is first loaded, and once
        per day in live trading. Use this to set up any internal state,
        indicators, zones, or other strategy-specific data structures.
        
        Args:
            current_price: Current market price
            historical_data: Historical OHLC data (pandas DataFrame)
        """
        pass
    
    @abstractmethod
    def update(self, current_price: float, current_time: Optional[datetime] = None) -> None:
        """Update strategy state with new market data.
        
        This is called periodically (every STRATEGY_INTERVAL seconds) to
        update any internal state based on new market conditions.
        
        Args:
            current_price: Current market price
            current_time: Current datetime (optional)
        """
        pass
    
    @abstractmethod
    def get_entry_signals(self, tick_price: float, prev_tick_price: Optional[float] = None, 
                         current_time: Optional[datetime] = None) -> List[Tuple[str, Dict[str, Any]]]:
        """Generate entry signals based on current market conditions.
        
        This is the core strategy logic that determines when to enter trades.
        
        Args:
            tick_price: Current tick price
            prev_tick_price: Previous tick price (for detecting crosses/changes)
            current_time: Current datetime
            
        Returns:
            List of tuples: [(signal_type, signal_data), ...]
            where signal_type is "Buy" or "Sell"
            and signal_data contains additional information about the signal
        """
        pass
    
    def get_exit_signals(self, open_positions: List[Dict], current_price: float, 
                        current_time: Optional[datetime] = None) -> List[Tuple[str, Dict]]:
        """Generate exit signals for open positions.
        
        Override this method if your strategy needs custom exit logic
        beyond standard SL/TP. Default implementation returns no signals.
        
        Args:
            open_positions: List of open position dictionaries
            current_price: Current market price
            current_time: Current datetime
            
        Returns:
            List of tuples: [(position_id, exit_data), ...]
        """
        return []
    
    def on_trade_opened(self, trade_data: Dict[str, Any]) -> None:
        """Called when a trade is successfully opened.
        
        Override this method if your strategy needs to track opened trades
        or update internal state when trades are executed.
        
        Args:
            trade_data: Dictionary containing trade information
        """
        pass
    
    def on_trade_closed(self, trade_data: Dict[str, Any]) -> None:
        """Called when a trade is closed.
        
        Override this method if your strategy needs to track closed trades
        or update internal state when trades are closed.
        
        Args:
            trade_data: Dictionary containing trade information including profit/loss
        """
        pass
    
    def get_position_size(self, signal_data: Dict[str, Any], trade_history: List[Dict]) -> float:
        """Calculate position size for a new trade.
        
        Override this method to implement custom position sizing logic.
        Default implementation uses the base lot size from config.
        
        Args:
            signal_data: Signal information from get_entry_signals
            trade_history: List of historical trades for martingale/progression logic
            
        Returns:
            Position size (lot size)
        """
        return getattr(self.config, 'BASE_LOT_SIZE', 0.01)
    
    def get_stop_loss(self, signal_type: str, entry_price: float, signal_data: Dict[str, Any]) -> float:
        """Calculate stop loss price for a new trade.
        
        Override this method to implement custom stop loss logic.
        Default implementation uses SL_POINTS from config.
        
        Args:
            signal_type: "Buy" or "Sell"
            entry_price: Entry price for the trade
            signal_data: Signal information from get_entry_signals
            
        Returns:
            Stop loss price
        """
        sl_points = getattr(self.config, 'SL_POINTS', 3.0)
        if signal_type == "Buy":
            return entry_price - sl_points
        else:  # Sell
            return entry_price + sl_points
    
    def get_take_profit(self, signal_type: str, entry_price: float, signal_data: Dict[str, Any]) -> float:
        """Calculate take profit price for a new trade.
        
        Override this method to implement custom take profit logic.
        Default implementation uses TP_POINTS from config.
        
        Args:
            signal_type: "Buy" or "Sell"
            entry_price: Entry price for the trade
            signal_data: Signal information from get_entry_signals
            
        Returns:
            Take profit price
        """
        tp_points = getattr(self.config, 'TP_POINTS', 3.0)
        if signal_type == "Buy":
            return entry_price + tp_points
        else:  # Sell
            return entry_price - tp_points
    
    def get_strategy_state(self) -> Dict[str, Any]:
        """Get current strategy state for debugging/monitoring.
        
        Override this method to return strategy-specific state information
        that can be useful for debugging or monitoring.
        
        Returns:
            Dictionary containing strategy state information
        """
        return {
            "strategy_name": self.__class__.__name__,
            "initialized": True
        }
    
    def get_display_info(self, current_price: float) -> str:
        """Get strategy display information for live trading output (optional override).
        
        This method allows strategies to provide custom display information
        that will be shown periodically during live trading instead of
        generic tick statistics.
        
        Args:
            current_price: Current market price
            
        Returns:
            Formatted string for display in live trading logs
        """
        strategy_name = self.__class__.__name__
        return f"📊 {strategy_name} | Price: {current_price:.2f}"
    
    def get_signal_description(self, side: str, signal_data: Dict[str, Any], current_price: float) -> str:
        """Get custom signal description for logging (optional override).
        
        This method allows strategies to provide custom signal descriptions
        that will be shown when signals are generated.
        
        Args:
            side: Trading side ('Buy' or 'Sell')
            signal_data: Signal data dictionary
            current_price: Current market price
            
        Returns:
            Formatted string describing the signal
        """
        signal_type = signal_data.get('signal_type', 'unknown')
        return f"📊 SIGNAL | {side.upper()} | Price: {current_price:.2f} | Type: {signal_type}"
