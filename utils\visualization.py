import matplotlib.pyplot as plt
import os
import logging
from datetime import datetime, timedelta
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd

log = logging.getLogger("ZoneBoS.Visualization")

def plot_backtest_summary(all_trades, start_date, end_date, symbol, base_trader):
    """Plot interactive candlestick chart for the backtest period with trade details.

    Args:
        all_trades: List of trade dictionaries
        start_date: Start date for the backtest period
        end_date: End date for the backtest period
        symbol: Trading symbol
        base_trader: BaseTrader instance for data fetching
    """
    if not all_trades:
        log.info("No trades to plot for the backtest period.")
        return

    # Fetch data starting 1 day before backtest for complete chart coverage
    fetch_start = start_date - timedelta(days=1)

    timeframe_str = getattr(base_trader.cfg, 'TIMEFRAME', 'M1')
    log.info(f"Fetching {timeframe_str} data from {fetch_start} to {end_date} (1 day buffer for complete chart)")
    df = base_trader.broker_get_historical_data(
        symbol=symbol,
        timeframe=base_trader.configured_timeframe,
        start_date=fetch_start,
        end_date=end_date
    )

    if df is None or df.empty:
        log.error(f"No historical data received for {fetch_start} to {end_date}.")
        return

    log.info(f"Fetched {len(df)} total M1 candles from {df.index[0]} to {df.index[-1]}")

    # Filter to exact backtest date range for trade markers, but keep all data for chart
    # This ensures we have complete candlestick coverage while trades are positioned correctly
    chart_df = df.copy()  # Keep all data for chart display
    trade_df = df[(df.index >= start_date) & (df.index <= end_date)]  # Filter for trade positioning

    if trade_df.empty:
        log.warning(f"No price data available for trade positioning {start_date} to {end_date}")
        return

    log.info(f"Using {len(chart_df)} M1 candles for chart display from {chart_df.index[0]} to {chart_df.index[-1]}")
    log.info(f"Trade positioning based on {len(trade_df)} M1 candles from {trade_df.index[0]} to {trade_df.index[-1]}")

    fig = make_subplots(rows=1, cols=1, shared_xaxes=True, vertical_spacing=0.03)

    # Add candlestick trace using full chart data for complete coverage
    fig.add_trace(go.Candlestick(
        x=chart_df.index,
        open=chart_df['open'],
        high=chart_df['high'],
        low=chart_df['low'],
        close=chart_df['close'],
        name='Candlesticks',
        increasing_line_color='green',
        decreasing_line_color='red'
    ))

    buy_entries = {'x': [], 'y': [], 'text': []}
    sell_entries = {'x': [], 'y': [], 'text': []}
    win_exits = {'x': [], 'y': [], 'text': []}
    loss_exits = {'x': [], 'y': [], 'text': []}
    shapes = []

    for trade in all_trades:
        entry_time = trade['entry_time']
        close_time = trade['close_time'] if trade['close_time'] is not None else end_date
        entry_price = trade['entry_price']
        sl = trade['sl']
        tp = trade['tp']
        trade_type = trade['type']
        status = trade['status']

        hover_text = (
            f"Type: {trade['type']}<br>"
            f"Entry Price: {entry_price:.2f}<br>"
            f"Lot: {trade['lot']:.2f}<br>"
            f"Time: {entry_time.strftime('%Y-%m-%d %H:%M:%S')}"
        )

        if trade_type == "Buy":
            buy_entries['x'].append(entry_time)
            buy_entries['y'].append(entry_price)
            buy_entries['text'].append(hover_text)
        else:
            sell_entries['x'].append(entry_time)
            sell_entries['y'].append(entry_price)
            sell_entries['text'].append(hover_text)

        if status in ["Win", "Loss"]:
            exit_text = (
                f"Type: {trade['type']}<br>"
                f"Exit Price: {trade['exit_price']:.2f}<br>"
                f"Profit: {trade['profit']:.2f}<br>"
                f"Status: {status}<br>"
                f"Time: {close_time.strftime('%Y-%m-%d %H:%M:%S')}"
            )
            if status == "Win":
                win_exits['x'].append(close_time)
                win_exits['y'].append(trade['exit_price'])
                win_exits['text'].append(exit_text)
            else:
                loss_exits['x'].append(close_time)
                loss_exits['y'].append(trade['exit_price'])
                loss_exits['text'].append(exit_text)

        shapes.append({
            'type': 'line',
            'x0': entry_time,
            'x1': close_time,
            'y0': sl,
            'y1': sl,
            'line': {'color': 'red', 'width': 1, 'dash': 'dash'}
        })
        shapes.append({
            'type': 'line',
            'x0': entry_time,
            'x1': close_time,
            'y0': tp,
            'y1': tp,
            'line': {'color': 'green', 'width': 1, 'dash': 'dash'}
        })

    fig.add_trace(go.Scatter(
        x=buy_entries['x'],
        y=buy_entries['y'],
        mode='markers',
        name='Buy Entry',
        marker=dict(symbol='triangle-up', size=10, color='green'),
        text=buy_entries['text'],
        hoverinfo='text'
    ))
    fig.add_trace(go.Scatter(
        x=sell_entries['x'],
        y=sell_entries['y'],
        mode='markers',
        name='Sell Entry',
        marker=dict(symbol='triangle-down', size=10, color='red'),
        text=sell_entries['text'],
        hoverinfo='text'
    ))
    fig.add_trace(go.Scatter(
        x=win_exits['x'],
        y=win_exits['y'],
        mode='markers',
        name='Win Exit',
        marker=dict(symbol='circle', size=10, color='green'),
        text=win_exits['text'],
        hoverinfo='text'
    ))
    fig.add_trace(go.Scatter(
        x=loss_exits['x'],
        y=loss_exits['y'],
        mode='markers',
        name='Loss Exit',
        marker=dict(symbol='circle', size=10, color='red'),
        text=loss_exits['text'],
        hoverinfo='text'
    ))

    # Calculate stats only for completed trades (matching backtest logic)
    completed_trades = [t for t in all_trades if t.get('status') in ['Win', 'Loss']]
    total_trades = len(completed_trades)
    wins = sum(1 for trade in completed_trades if trade['status'] == "Win")
    losses = sum(1 for trade in completed_trades if trade['status'] == "Loss")
    win_ratio = 100 * wins / total_trades if total_trades else 0
    total_profit = sum(trade['profit'] for trade in all_trades if trade.get('profit'))

    summary_text = (
        f"<b>Backtest Summary</b><br>"
        f"Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}<br>"
        f"Total Trades: {total_trades}<br>"
        f"Wins: {wins} ({win_ratio:.2f}%)<br>"
        f"Losses: {losses}<br>"
        f"Total Profit: ${total_profit:.2f}"
    )
    fig.add_annotation(
        xref="paper", yref="paper",
        x=0.02, y=0.98,
        text=summary_text,
        showarrow=False,
        font=dict(size=12),
        align="left",
        bgcolor="white",
        bordercolor="black",
        borderwidth=1
    )

    # Use exact layout from mainExample.py
    fig.update_layout(
        title=f"Interactive Backtest Chart for {symbol} ({start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')})",
        xaxis_title="Time",
        yaxis_title="Price",
        xaxis_rangeslider_visible=False,
        showlegend=True,
        template="plotly_white",
        height=800,
        xaxis=dict(
            tickangle=45,
            type='date'
        )
    )

    fig.update_layout(shapes=shapes)

    # Define a default filename
    filename = "backtest_chart.html"

    fig.write_html(filename)
    log.info(f"Interactive chart saved as '{filename}'")
    fig.show()
    log.info("Interactive candlestick chart displayed in browser.")