# TradingBot Requirements
# Core dependencies for the MetaTrader 5 Trading Bot

# MetaTrader 5 Integration
MetaTrader5==5.0.45

# Data Analysis and Manipulation
pandas==2.1.2
numpy==1.26.1

# Visualization Libraries
matplotlib>=3.5.0
plotly==5.17.0

# Timezone Handling
pytz==2023.3.post1

# Standard Library Dependencies (included with Python)
# These are listed for documentation purposes - no installation needed:
# - logging (built-in)
# - datetime (built-in)
# - time (built-in)
# - csv (built-in)
# - os (built-in)
# - argparse (built-in)
# - math (built-in)

# Development and Testing (optional)
# pytest>=7.0.0
# pytest-cov>=4.0.0
# pytest-mock>=3.10.0
# black>=22.0.0
# flake8>=4.0.0

# Installation Instructions:
# 1. Install Python 3.8 or higher
# 2. Install MetaTrader 5 terminal from MetaQuotes
# 3. Run: pip install -r requirements.txt
# 4. Configure your MT5 credentials in config.py
# 5. Test with demo account first before live trading
